namespace CommandGuard.Models;

/// <summary>
/// 订单统计信息模型
/// 用于统计各种订单的数量和金额信息
/// </summary>
public class OrderStatistics
{
    /// <summary>
    /// 待审核上分申请数量
    /// </summary>
    public int PendingDepositCount { get; set; }

    /// <summary>
    /// 待审核下分申请数量
    /// </summary>
    public int PendingWithdrawCount { get; set; }

    /// <summary>
    /// 今日上分总金额
    /// </summary>
    public decimal TodayDepositAmount { get; set; }

    /// <summary>
    /// 今日下分总金额
    /// </summary>
    public decimal TodayWithdrawAmount { get; set; }

    /// <summary>
    /// 今日处理订单数量
    /// </summary>
    public int TodayProcessedCount { get; set; }

    /// <summary>
    /// 待处理投注订单数量
    /// </summary>
    public int PendingBetOrderCount { get; set; }

    /// <summary>
    /// 今日投注总金额
    /// </summary>
    public decimal TodayBetAmount { get; set; }

    /// <summary>
    /// 今日中奖总金额
    /// </summary>
    public decimal TodayWinAmount { get; set; }

    /// <summary>
    /// 获取今日净盈亏
    /// </summary>
    /// <returns>净盈亏金额（投注金额 - 中奖金额）</returns>
    public decimal GetTodayNetProfit()
    {
        return TodayBetAmount - TodayWinAmount;
    }

    /// <summary>
    /// 获取今日资金流入
    /// </summary>
    /// <returns>资金流入金额（上分金额 + 投注金额）</returns>
    public decimal GetTodayInflow()
    {
        return TodayDepositAmount + TodayBetAmount;
    }

    /// <summary>
    /// 获取今日资金流出
    /// </summary>
    /// <returns>资金流出金额（下分金额 + 中奖金额）</returns>
    public decimal GetTodayOutflow()
    {
        return TodayWithdrawAmount + TodayWinAmount;
    }

    /// <summary>
    /// 获取待处理订单总数
    /// </summary>
    /// <returns>待处理订单总数</returns>
    public int GetTotalPendingCount()
    {
        return PendingDepositCount + PendingWithdrawCount + PendingBetOrderCount;
    }
}
