using System.ComponentModel;

namespace CommandGuard.ViewModels;

/// <summary>
/// 指令限额视图模型
/// 用于在DataGridView中显示系统支持的指令和投注项目限额
/// </summary>
public class CommandLimitViewModel
{
    /// <summary>
    /// 玩法类型
    /// </summary>
    [DisplayName(@"玩法类型")]
    public string PlayType { get; set; } = string.Empty;

    /// <summary>
    /// 识别指令
    /// </summary>
    [DisplayName(@"识别指令")]
    public string Command { get; set; } = string.Empty;

    /// <summary>
    /// 赔率（仅投注项目有值）
    /// </summary>
    [DisplayName(@"赔率")]
    public string Odds { get; set; } = string.Empty;

    /// <summary>
    /// 单笔最低注额（仅投注项目有值）
    /// </summary>
    [DisplayName(@"单笔最低注额")]
    public string MinStake { get; set; } = string.Empty;

    /// <summary>
    /// 单笔最高注额（仅投注项目有值）
    /// </summary>
    [DisplayName(@"单笔最高注额")]
    public string MaxStake { get; set; } = string.Empty;

    /// <summary>
    /// 单局限额（仅投注项目有值）
    /// </summary>
    [DisplayName(@"单局限额")]
    public string TotalStake { get; set; } = string.Empty;

    /// <summary>
    /// 排序顺序（用于排序，不显示）
    /// </summary>
    public int SortOrder { get; set; }

    /// <summary>
    /// 是否为投注项目（用于区分指令和投注项目，不显示）
    /// </summary>
    public bool IsBettingItem { get; set; }

    /// <summary>
    /// 原始投注项目名称（用于更新数据库，不显示）
    /// </summary>
    public string OriginalPlayItem { get; set; } = string.Empty;

    /// <summary>
    /// 原始赔率值（用于比较是否有变化，不显示）
    /// </summary>
    public decimal OriginalOdds { get; set; }

    /// <summary>
    /// 原始最低注额（用于比较是否有变化，不显示）
    /// </summary>
    public decimal OriginalMinStake { get; set; }

    /// <summary>
    /// 原始最高注额（用于比较是否有变化，不显示）
    /// </summary>
    public decimal OriginalMaxStake { get; set; }

    /// <summary>
    /// 原始单局限额（用于比较是否有变化，不显示）
    /// </summary>
    public decimal OriginalTotalStake { get; set; }
}
