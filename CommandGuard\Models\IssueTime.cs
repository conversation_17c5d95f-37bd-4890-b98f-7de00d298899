﻿using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

/// <summary>
/// 发放时间实体类
/// 表示一个时间发放的时间段信息
/// </summary>
[Table(Name = "IssueTime")]
[Index("IX_IssueTime_OpenTime_CloseTime", nameof(OpenTime) + "," + nameof(CloseTime), false)]
[Index("IX_IssueTime_OpenTime", nameof(OpenTime), false)]
[Index("IX_IssueTime_Issue", nameof(Issue), true)]
public class IssueTime
{
    /// <summary>
    /// 主键ID，自增长
    /// </summary>
    [Column(IsPrimary = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 发放编号，格式：民国年份+6位序号（如：113000001）
    /// 每个发放编号都是唯一的，添加唯一索引以提高查询性能和保证数据完整性
    /// </summary>
    [Column(StringLength = 50)]
    public string Issue { get; set; } = string.Empty;

    /// <summary>
    /// 发放开放时间
    /// 这是最常用的查询字段，添加索引以提高按时间查询的性能
    /// </summary>
    public DateTime OpenTime { get; set; }

    /// <summary>
    /// 发放关闭时间
    /// 与OpenTime组合用于时间范围查询，添加复合索引以优化性能
    /// </summary>
    public DateTime CloseTime { get; set; }
}