using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using CommandGuard.Licensing.Models;

namespace CommandGuard.Licensing.Services;

/// <summary>
/// 授权验证服务
/// 负责授权文件的生成、验证和管理
/// </summary>
public static class LicenseService
{
    /// <summary>
    /// 授权文件名
    /// </summary>
    public const string LicenseFileName = "CommandGuard.lic";

    /// <summary>
    /// 私钥（用于生成数字签名）
    /// 注意：实际项目中应该妥善保管私钥
    /// </summary>
    private const string PrivateKey = "CommandGuard_License_Private_Key_2024";

    /// <summary>
    /// 生成授权文件
    /// </summary>
    /// <param name="licenseInfo">授权信息</param>
    /// <param name="filePath">保存路径</param>
    /// <returns>是否生成成功</returns>
    public static bool GenerateLicenseFile(LicenseInfo licenseInfo, string filePath)
    {
        try
        {
            // 生成数字签名
            licenseInfo.Signature = GenerateSignature(licenseInfo);

            // 序列化为JSON
            var jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };
            var jsonContent = JsonSerializer.Serialize(licenseInfo, jsonOptions);

            // 加密内容
            var encryptedContent = EncryptContent(jsonContent);

            // 写入文件
            File.WriteAllText(filePath, encryptedContent);

            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 验证授权文件
    /// </summary>
    /// <param name="filePath">授权文件路径</param>
    /// <returns>验证结果</returns>
    public static LicenseValidationResult ValidateLicenseFile(string filePath)
    {
        try
        {
            // 检查文件是否存在
            if (!File.Exists(filePath))
            {
                return new LicenseValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "授权文件不存在"
                };
            }

            // 读取并解密文件内容
            var encryptedContent = File.ReadAllText(filePath);
            var jsonContent = DecryptContent(encryptedContent);

            if (string.IsNullOrEmpty(jsonContent))
            {
                return new LicenseValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "授权文件格式错误"
                };
            }

            // 反序列化授权信息
            var licenseInfo = JsonSerializer.Deserialize<LicenseInfo>(jsonContent);
            if (licenseInfo == null)
            {
                return new LicenseValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "授权文件内容无效"
                };
            }

            // 验证数字签名
            if (!VerifySignature(licenseInfo))
            {
                return new LicenseValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "授权文件签名验证失败"
                };
            }

            // 验证机器码
            if (!MachineCodeService.ValidateMachineCode(licenseInfo.MachineCode))
            {
                return new LicenseValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "机器码不匹配，此授权文件不适用于当前机器"
                };
            }

            // 验证授权是否有效
            if (!licenseInfo.IsValid())
            {
                return new LicenseValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "授权已过期或信息不完整"
                };
            }

            return new LicenseValidationResult
            {
                IsValid = true,
                LicenseInfo = licenseInfo,
                ErrorMessage = string.Empty
            };
        }
        catch (Exception ex)
        {
            return new LicenseValidationResult
            {
                IsValid = false,
                ErrorMessage = $"验证授权文件时发生错误：{ex.Message}"
            };
        }
    }

    /// <summary>
    /// 生成数字签名
    /// </summary>
    private static string GenerateSignature(LicenseInfo licenseInfo)
    {
        // 创建签名内容（排除签名字段本身）
        var signatureContent = $"{licenseInfo.MachineCode}{licenseInfo.LicensedTo}{licenseInfo.ProductName}" +
                              $"{licenseInfo.ProductVersion}{licenseInfo.IssuedAt:yyyy-MM-dd}{licenseInfo.ExpiresAt:yyyy-MM-dd}" +
                              $"{licenseInfo.LicenseType}{licenseInfo.MaxUsers}{licenseInfo.IsPermanent}{licenseInfo.SerialNumber}";

        // 使用HMAC-SHA256生成签名
        using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(PrivateKey));
        var signatureBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(signatureContent));
        return Convert.ToBase64String(signatureBytes);
    }

    /// <summary>
    /// 验证数字签名
    /// </summary>
    private static bool VerifySignature(LicenseInfo licenseInfo)
    {
        var originalSignature = licenseInfo.Signature;
        licenseInfo.Signature = string.Empty; // 临时清空签名

        var expectedSignature = GenerateSignature(licenseInfo);
        licenseInfo.Signature = originalSignature; // 恢复签名

        return string.Equals(originalSignature, expectedSignature, StringComparison.Ordinal);
    }

    /// <summary>
    /// 加密内容
    /// </summary>
    private static string EncryptContent(string content)
    {
        try
        {
            var key = Encoding.UTF8.GetBytes(PrivateKey.PadRight(32)[..32]);
            var iv = Encoding.UTF8.GetBytes("CommandGuard_IV_".PadRight(16)[..16]);

            using var aes = Aes.Create();
            aes.Key = key;
            aes.IV = iv;

            using var encryptor = aes.CreateEncryptor();
            var contentBytes = Encoding.UTF8.GetBytes(content);
            var encryptedBytes = encryptor.TransformFinalBlock(contentBytes, 0, contentBytes.Length);

            return Convert.ToBase64String(encryptedBytes);
        }
        catch
        {
            return string.Empty;
        }
    }

    /// <summary>
    /// 解密内容
    /// </summary>
    private static string DecryptContent(string encryptedContent)
    {
        try
        {
            var key = Encoding.UTF8.GetBytes(PrivateKey.PadRight(32)[..32]);
            var iv = Encoding.UTF8.GetBytes("CommandGuard_IV_".PadRight(16)[..16]);

            using var aes = Aes.Create();
            aes.Key = key;
            aes.IV = iv;

            using var decryptor = aes.CreateDecryptor();
            var encryptedBytes = Convert.FromBase64String(encryptedContent);
            var decryptedBytes = decryptor.TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);

            return Encoding.UTF8.GetString(decryptedBytes);
        }
        catch
        {
            return string.Empty;
        }
    }
}

/// <summary>
/// 授权验证结果
/// </summary>
public class LicenseValidationResult
{
    /// <summary>
    /// 是否验证通过
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 授权信息（验证通过时有值）
    /// </summary>
    public LicenseInfo? LicenseInfo { get; set; }

    /// <summary>
    /// 错误信息（验证失败时有值）
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;
}
