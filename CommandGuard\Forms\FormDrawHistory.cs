using Microsoft.Extensions.Logging;

namespace CommandGuard.Forms;

/// <summary>
/// 开奖历史窗口
/// 显示程序运行目录中Images目录下的draw.jpeg图片
/// </summary>
public partial class FormDrawHistory : Form
{
    #region 字段和属性

    private readonly ILogger<FormDrawHistory> _logger;
    private readonly string _drawImagePath;

    #endregion

    #region 构造函数

    /// <summary>
    /// 构造函数
    /// </summary>
    public FormDrawHistory(ILogger<FormDrawHistory> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // 获取程序运行目录中的Images/draw.jpeg路径
        var appDirectory = AppDomain.CurrentDomain.BaseDirectory;
        var imagesDirectory = Path.Combine(appDirectory, "Images");
        _drawImagePath = Path.Combine(imagesDirectory, "draw.jpeg");

        InitializeComponent();
        InitializeImageDisplay();
    }

    #endregion

    #region 初始化

    /// <summary>
    /// 初始化图片显示
    /// </summary>
    private void InitializeImageDisplay()
    {
        try
        {
            // 设置PictureBox属性
            pictureBox_DrawHistory.SizeMode = PictureBoxSizeMode.AutoSize; // 自动调整大小
            pictureBox_DrawHistory.BorderStyle = BorderStyle.FixedSingle;
            pictureBox_DrawHistory.BackColor = Color.White;
            pictureBox_DrawHistory.Dock = DockStyle.None; // 不使用Dock，让AutoSize生效

            // 设置窗口属性
            WindowState = FormWindowState.Normal; // 正常窗口状态
            KeyPreview = true; // 启用键盘事件
            AutoSize = true; // 自动调整窗口大小
            AutoSizeMode = AutoSizeMode.GrowAndShrink; // 根据内容自动增长和收缩
            FormBorderStyle = FormBorderStyle.FixedDialog; // 固定边框，防止用户调整大小
            MaximizeBox = false; // 禁用最大化按钮

            _logger.LogDebug(@"开奖历史窗口初始化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"初始化开奖历史窗口失败");
        }
    }

    #endregion

    #region 图片加载

    /// <summary>
    /// 加载开奖历史图片
    /// </summary>
    private void LoadDrawHistoryImage()
    {
        try
        {
            _logger.LogInformation(@"开始加载开奖历史图片，路径: {Path}", _drawImagePath);

            // 检查图片文件是否存在
            if (!File.Exists(_drawImagePath))
            {
                _logger.LogWarning(@"开奖历史图片文件不存在，路径: {Path}", _drawImagePath);
                ShowNoImageMessage();
                return;
            }

            // 检查文件大小
            var fileInfo = new FileInfo(_drawImagePath);
            if (fileInfo.Length == 0)
            {
                _logger.LogWarning(@"开奖历史图片文件为空，路径: {Path}", _drawImagePath);
                ShowNoImageMessage();
                return;
            }

            // 加载图片
            using var fileStream = new FileStream(_drawImagePath, FileMode.Open, FileAccess.Read);
            var image = Image.FromStream(fileStream);

            // 显示图片
            pictureBox_DrawHistory.Image?.Dispose(); // 释放之前的图片
            pictureBox_DrawHistory.Image = image;

            // 调整窗口大小以适应图片
            AdjustWindowSizeToImage(image);

            // 更新窗口标题
            Text = $@"开奖历史";

            _logger.LogInformation(@"开奖历史图片加载成功，尺寸: {Width}x{Height}", image.Width, image.Height);
        }
        catch (OutOfMemoryException ex)
        {
            _logger.LogError(ex, @"加载开奖历史图片失败，可能不是有效的图片文件");
            ShowErrorMessage("图片文件格式不正确或已损坏");
        }
        catch (FileNotFoundException ex)
        {
            _logger.LogError(ex, @"开奖历史图片文件未找到");
            ShowNoImageMessage();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogError(ex, @"没有权限访问开奖历史图片文件");
            ShowErrorMessage("没有权限访问图片文件");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"加载开奖历史图片时发生未知错误");
            ShowErrorMessage($"加载图片失败：{ex.Message}");
        }
    }

    /// <summary>
    /// 显示无图片消息
    /// </summary>
    private void ShowNoImageMessage()
    {
        try
        {
            // 清空PictureBox
            pictureBox_DrawHistory.Image?.Dispose();
            pictureBox_DrawHistory.Image = null;

            // 创建提示文本
            var noImageText = $@"开奖历史图片不存在" + Environment.NewLine +
                             $@"请确保以下文件存在：" + Environment.NewLine +
                             $@"{_drawImagePath}";

            // 设置合适的窗口大小
            Size = new Size(500, 300);
            CenterToParent();

            Text = @"开奖历史 - 图片不存在";

            _logger.LogInformation(@"显示无图片提示消息");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"显示无图片消息失败");
        }
    }

    /// <summary>
    /// 显示错误消息
    /// </summary>
    private void ShowErrorMessage(string message)
    {
        try
        {
            // 清空PictureBox
            pictureBox_DrawHistory.Image?.Dispose();
            pictureBox_DrawHistory.Image = null;
            
            // 设置合适的窗口大小
            Size = new Size(500, 300);
            CenterToParent();

            Text = @"开奖历史 - 加载失败";

            _logger.LogInformation(@"显示错误消息: {Message}", message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"显示错误消息失败");
        }
    }

    /// <summary>
    /// 调整窗口大小以适应图片
    /// </summary>
    private void AdjustWindowSizeToImage(Image image)
    {
        try
        {
            // 获取屏幕工作区域大小
            var screenBounds = Screen.FromControl(this).WorkingArea;

            // 计算最大允许的图片尺寸（屏幕的90%减去窗口额外空间）
            var maxImageWidth = (int)(screenBounds.Width * 0.9) - 40; // 减去边框和边距
            var maxImageHeight = (int)(screenBounds.Height * 0.9) - 100; // 减去标题栏、边框和边距

            // 检查图片是否需要缩放
            if (image.Width > maxImageWidth || image.Height > maxImageHeight)
            {
                // 图片太大，需要缩放显示
                pictureBox_DrawHistory.SizeMode = PictureBoxSizeMode.Zoom;
                pictureBox_DrawHistory.Dock = DockStyle.None;

                // 计算缩放比例
                var scaleX = (double)maxImageWidth / image.Width;
                var scaleY = (double)maxImageHeight / image.Height;
                var scale = Math.Min(scaleX, scaleY);

                // 设置PictureBox的大小
                var scaledWidth = (int)(image.Width * scale);
                var scaledHeight = (int)(image.Height * scale);
                pictureBox_DrawHistory.Size = new Size(scaledWidth, scaledHeight);

                _logger.LogDebug(@"图片缩放显示，原始: {OriginalWidth}x{OriginalHeight}，缩放后: {ScaledWidth}x{ScaledHeight}",
                    image.Width, image.Height, scaledWidth, scaledHeight);
            }
            else
            {
                // 图片大小合适，使用原始大小
                pictureBox_DrawHistory.SizeMode = PictureBoxSizeMode.AutoSize;
                pictureBox_DrawHistory.Dock = DockStyle.None;

                _logger.LogDebug(@"图片原始大小显示: {Width}x{Height}", image.Width, image.Height);
            }

            // 让窗口自动调整大小
            AutoSize = true;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;

            // 强制重新计算布局
            PerformLayout();

            // 延迟居中，等待布局完成
            BeginInvoke(new Action(() =>
            {
                CenterToParent();
                _logger.LogDebug(@"窗口最终大小: {Width}x{Height}", Width, Height);
            }));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"调整窗口大小失败");
        }
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// 窗口加载事件
    /// </summary>
    private void FormDrawHistory_Load(object sender, EventArgs e)
    {
        LoadDrawHistoryImage();
    }

    /// <summary>
    /// 刷新按钮点击事件
    /// </summary>
    private void button_Refresh_Click(object sender, EventArgs e)
    {
        try
        {
            _logger.LogInformation(@"用户点击刷新按钮，重新加载开奖历史图片");
            
            // 隐藏消息标签
            // label_Message.Visible = false;
            
            // 重新加载图片
            LoadDrawHistoryImage();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"刷新开奖历史图片失败");
            ShowErrorMessage($"刷新失败：{ex.Message}");
        }
    }
    
    /// <summary>
    /// 键盘按键事件
    /// </summary>
    private void FormDrawHistory_KeyDown(object sender, KeyEventArgs e)
    {
        try
        {
            switch (e.KeyCode)
            {
                case Keys.Escape:
                    // ESC键关闭窗口
                    Close();
                    break;
                case Keys.F5:
                    // F5键刷新图片
                    button_Refresh_Click(sender, e);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"处理键盘事件失败");
        }
    }

    /// <summary>
    /// 窗口关闭事件
    /// </summary>
    private void FormDrawHistory_FormClosing(object sender, FormClosingEventArgs e)
    {
        try
        {
            // 释放图片资源
            pictureBox_DrawHistory.Image?.Dispose();
            pictureBox_DrawHistory.Image = null;

            _logger.LogDebug(@"开奖历史窗口关闭，已释放图片资源");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"关闭开奖历史窗口时释放资源失败");
        }
    }

    #endregion
}
