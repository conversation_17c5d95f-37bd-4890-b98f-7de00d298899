using System.Management;
using System.Security.Cryptography;
using System.Text;

namespace CommandGuard.Licensing.Services;

/// <summary>
/// 机器码生成服务
/// 基于硬件信息生成唯一的机器标识码
/// </summary>
public static class MachineCodeService
{
    /// <summary>
    /// 生成当前机器的唯一机器码
    /// 基于CPU、主板、硬盘等硬件信息
    /// </summary>
    /// <returns>机器码字符串</returns>
    public static string GenerateMachineCode()
    {
        try
        {
            var hardwareInfo = new StringBuilder();

            // 获取CPU信息
            var cpuInfo = GetCpuInfo();
            hardwareInfo.Append(cpuInfo);

            // 获取主板信息
            var motherboardInfo = GetMotherboardInfo();
            hardwareInfo.Append(motherboardInfo);

            // 获取硬盘信息
            var diskInfo = GetDiskInfo();
            hardwareInfo.Append(diskInfo);

            // 获取网卡MAC地址
            var macInfo = GetMacAddress();
            hardwareInfo.Append(macInfo);

            // 生成SHA256哈希
            using var sha256 = SHA256.Create();
            var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(hardwareInfo.ToString()));
            
            // 转换为16进制字符串并格式化
            var machineCode = Convert.ToHexString(hashBytes);
            
            // 格式化为更易读的格式：XXXX-XXXX-XXXX-XXXX
            return FormatMachineCode(machineCode);
        }
        catch (Exception)
        {
            // 如果获取硬件信息失败，使用备用方案
            return GenerateFallbackMachineCode();
        }
    }

    /// <summary>
    /// 验证机器码是否匹配当前机器
    /// </summary>
    /// <param name="machineCode">要验证的机器码</param>
    /// <returns>是否匹配</returns>
    public static bool ValidateMachineCode(string machineCode)
    {
        try
        {
            var currentMachineCode = GenerateMachineCode();
            return string.Equals(machineCode, currentMachineCode, StringComparison.OrdinalIgnoreCase);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取CPU信息
    /// </summary>
    private static string GetCpuInfo()
    {
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor");
            foreach (ManagementObject obj in searcher.Get())
            {
                return obj["ProcessorId"]?.ToString() ?? "";
            }
        }
        catch
        {
            // 忽略异常
        }
        return Environment.ProcessorCount.ToString();
    }

    /// <summary>
    /// 获取主板信息
    /// </summary>
    private static string GetMotherboardInfo()
    {
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard");
            foreach (ManagementObject obj in searcher.Get())
            {
                return obj["SerialNumber"]?.ToString() ?? "";
            }
        }
        catch
        {
            // 忽略异常
        }
        return Environment.MachineName;
    }

    /// <summary>
    /// 获取硬盘信息
    /// </summary>
    private static string GetDiskInfo()
    {
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_DiskDrive WHERE MediaType='Fixed hard disk media'");
            foreach (ManagementObject obj in searcher.Get())
            {
                var serialNumber = obj["SerialNumber"]?.ToString();
                if (!string.IsNullOrEmpty(serialNumber))
                {
                    return serialNumber;
                }
            }
        }
        catch
        {
            // 忽略异常
        }
        return Environment.SystemDirectory;
    }

    /// <summary>
    /// 获取网卡MAC地址
    /// </summary>
    private static string GetMacAddress()
    {
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT MACAddress FROM Win32_NetworkAdapter WHERE NetConnectionStatus=2");
            foreach (ManagementObject obj in searcher.Get())
            {
                var macAddress = obj["MACAddress"]?.ToString();
                if (!string.IsNullOrEmpty(macAddress))
                {
                    return macAddress;
                }
            }
        }
        catch
        {
            // 忽略异常
        }
        return Environment.UserName;
    }

    /// <summary>
    /// 格式化机器码为易读格式
    /// </summary>
    private static string FormatMachineCode(string machineCode)
    {
        if (machineCode.Length < 16)
        {
            machineCode = machineCode.PadRight(16, '0');
        }

        // 取前16位并格式化为 XXXX-XXXX-XXXX-XXXX
        var formatted = machineCode[..16];
        return $"{formatted[..4]}-{formatted[4..8]}-{formatted[8..12]}-{formatted[12..16]}";
    }

    /// <summary>
    /// 备用机器码生成方案
    /// 当无法获取硬件信息时使用
    /// </summary>
    private static string GenerateFallbackMachineCode()
    {
        var fallbackInfo = $"{Environment.MachineName}{Environment.UserName}{Environment.ProcessorCount}{Environment.OSVersion}";
        
        using var sha256 = SHA256.Create();
        var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(fallbackInfo));
        var machineCode = Convert.ToHexString(hashBytes);
        
        return FormatMachineCode(machineCode);
    }
}
