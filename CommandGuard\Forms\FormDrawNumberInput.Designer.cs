namespace CommandGuard.Forms
{
    partial class FormDrawNumberInput
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            label_Title = new Label();
            label_Issue = new Label();
            label_Instruction = new Label();
            textBox_DrawNumbers = new TextBox();
            button_OK = new Button();
            button_Cancel = new Button();
            panel_Buttons = new Panel();
            panel_Buttons.SuspendLayout();
            SuspendLayout();
            // 
            // label_Title
            // 
            label_Title.AutoSize = true;
            label_Title.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            label_Title.Location = new Point(12, 15);
            label_Title.Name = "label_Title";
            label_Title.Size = new Size(154, 22);
            label_Title.TabIndex = 0;
            label_Title.Text = "手动录入开奖号码";
            // 
            // label_Issue
            // 
            label_Issue.AutoSize = true;
            label_Issue.Font = new Font("Microsoft YaHei UI", 10F);
            label_Issue.Location = new Point(12, 50);
            label_Issue.Name = "label_Issue";
            label_Issue.Size = new Size(51, 20);
            label_Issue.TabIndex = 1;
            label_Issue.Text = "期号：";
            // 
            // label_Instruction
            // 
            label_Instruction.AutoSize = true;
            label_Instruction.Font = new Font("Microsoft YaHei UI", 9F);
            label_Instruction.ForeColor = Color.Gray;
            label_Instruction.Location = new Point(12, 80);
            label_Instruction.Name = "label_Instruction";
            label_Instruction.Size = new Size(344, 17);
            label_Instruction.TabIndex = 2;
            label_Instruction.Text = "请输入20个开奖号码（1-80），用逗号分隔，如：01,02,03...";
            // 
            // textBox_DrawNumbers
            // 
            textBox_DrawNumbers.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            textBox_DrawNumbers.Font = new Font("Microsoft YaHei UI", 10F);
            textBox_DrawNumbers.Location = new Point(12, 110);
            textBox_DrawNumbers.Multiline = true;
            textBox_DrawNumbers.Name = "textBox_DrawNumbers";
            textBox_DrawNumbers.ScrollBars = ScrollBars.Vertical;
            textBox_DrawNumbers.Size = new Size(460, 80);
            textBox_DrawNumbers.TabIndex = 3;
            textBox_DrawNumbers.KeyDown += textBox_DrawNumbers_KeyDown;
            // 
            // button_OK
            // 
            button_OK.Font = new Font("Microsoft YaHei UI", 10F);
            button_OK.Location = new Point(120, 10);
            button_OK.Name = "button_OK";
            button_OK.Size = new Size(80, 35);
            button_OK.TabIndex = 4;
            button_OK.Text = "确定";
            button_OK.UseVisualStyleBackColor = true;
            button_OK.Click += button_OK_Click;
            // 
            // button_Cancel
            // 
            button_Cancel.Font = new Font("Microsoft YaHei UI", 10F);
            button_Cancel.Location = new Point(220, 10);
            button_Cancel.Name = "button_Cancel";
            button_Cancel.Size = new Size(80, 35);
            button_Cancel.TabIndex = 5;
            button_Cancel.Text = "取消";
            button_Cancel.UseVisualStyleBackColor = true;
            button_Cancel.Click += button_Cancel_Click;
            // 
            // panel_Buttons
            // 
            panel_Buttons.Controls.Add(button_OK);
            panel_Buttons.Controls.Add(button_Cancel);
            panel_Buttons.Dock = DockStyle.Bottom;
            panel_Buttons.Location = new Point(0, 210);
            panel_Buttons.Name = "panel_Buttons";
            panel_Buttons.Size = new Size(484, 55);
            panel_Buttons.TabIndex = 6;
            // 
            // FormDrawNumberInput
            // 
            AutoScaleDimensions = new SizeF(7F, 17F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(484, 265);
            Controls.Add(panel_Buttons);
            Controls.Add(textBox_DrawNumbers);
            Controls.Add(label_Instruction);
            Controls.Add(label_Issue);
            Controls.Add(label_Title);
            Font = new Font("Microsoft YaHei UI", 9F);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "FormDrawNumberInput";
            ShowIcon = false;
            ShowInTaskbar = false;
            StartPosition = FormStartPosition.CenterParent;
            Text = "手动录入开奖号码";
            panel_Buttons.ResumeLayout(false);
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Label label_Title;
        private Label label_Issue;
        private Label label_Instruction;
        private TextBox textBox_DrawNumbers;
        private Button button_OK;
        private Button button_Cancel;
        private Panel panel_Buttons;
    }
}
