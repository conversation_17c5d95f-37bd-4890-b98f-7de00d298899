using System.ComponentModel.DataAnnotations;
using CommandGuard.Enums;
using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

/// <summary>
/// 上分申请模型
/// 用户申请增加余额的订单记录
/// </summary>
[Table(Name = @"DepositRequests")]
[Index(@"idx_deposit_account", nameof(Account))]
[Index(@"idx_deposit_status", nameof(Status))]
[Index(@"idx_deposit_created", nameof(CreatedTime))]
public class DepositRequest
{
    /// <summary>
    /// 申请ID，自增主键
    /// </summary>
    [Column(IsIdentity = true, IsPrimary = true)]
    public long Id { get; set; }

    /// <summary>
    /// 申请用户账号
    /// </summary>
    [Required(ErrorMessage = @"用户账号不能为空")]
    [StringLength(50, ErrorMessage = @"用户账号长度不能超过50个字符")]
    [Column(StringLength = 50)]
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 申请金额
    /// </summary>
    [Range(0.01, double.MaxValue, ErrorMessage = @"申请金额必须大于0")]
    [Column(Precision = 18, Scale = 2)]
    public decimal Amount { get; set; }

    /// <summary>
    /// 申请状态
    /// </summary>
    public EnumDepositStatus Status { get; set; } = EnumDepositStatus.Pending;

    /// <summary>
    /// 关联的消息ID
    /// </summary>
    public string MessageId { get; set; }=string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    [Column(ServerTime = DateTimeKind.Local)]
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 审核时间
    /// </summary>
    public DateTime? ReviewTime { get; set; }
}
