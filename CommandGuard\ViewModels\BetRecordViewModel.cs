using System.ComponentModel;
using CommandGuard.Enums;

namespace CommandGuard.ViewModels;

/// <summary>
/// 投注记录视图模型
/// 用于在投注记录查询界面显示投注数据
/// </summary>
public class BetRecordViewModel : INotifyPropertyChanged
{
    private long _id;
    private string _orderNumber = string.Empty;
    private string _nickName = string.Empty;
    private string _account = string.Empty;
    private string _issue = string.Empty;
    private string _playItem = string.Empty;
    private decimal _amount;
    private decimal _odds;
    private string _winningNumbers = string.Empty;
    private decimal _rebateAmount;
    private decimal? _actualWinAmount;
    private EnumBetOrderStatus _settlementStatus;
    private DateTime _createdTime;
    private bool _isRealOrder = true;

    /// <summary>
    /// 投注记录ID
    /// </summary>
    public long Id
    {
        get => _id;
        set
        {
            _id = value;
            OnPropertyChanged(nameof(Id));
        }
    }

    /// <summary>
    /// 注单号
    /// 唯一标识每笔投注的编号
    /// </summary>
    public string OrderNumber
    {
        get => _orderNumber;
        set
        {
            _orderNumber = value ?? string.Empty;
            OnPropertyChanged(nameof(OrderNumber));
        }
    }

    /// <summary>
    /// 用户昵称
    /// </summary>
    public string NickName
    {
        get => _nickName;
        set
        {
            _nickName = value ?? string.Empty;
            OnPropertyChanged(nameof(NickName));
        }
    }

    /// <summary>
    /// 用户账号
    /// </summary>
    public string Account
    {
        get => _account;
        set
        {
            _account = value ?? string.Empty;
            OnPropertyChanged(nameof(Account));
        }
    }

    /// <summary>
    /// 期数
    /// 投注的彩票期号
    /// </summary>
    public string Issue
    {
        get => _issue;
        set
        {
            _issue = value ?? string.Empty;
            OnPropertyChanged(nameof(Issue));
        }
    }

    /// <summary>
    /// 类型
    /// 具体的投注内容，如"1正"、"2番"、"单"、"双"等
    /// </summary>
    public string PlayItem
    {
        get => _playItem;
        set
        {
            _playItem = value ?? string.Empty;
            OnPropertyChanged(nameof(PlayItem));
        }
    }

    /// <summary>
    /// 金额
    /// 投注金额
    /// </summary>
    public decimal Amount
    {
        get => _amount;
        set
        {
            _amount = value;
            OnPropertyChanged(nameof(Amount));
        }
    }

    /// <summary>
    /// 赔率
    /// 该投注项的赔率
    /// </summary>
    public decimal Odds
    {
        get => _odds;
        set
        {
            _odds = value;
            OnPropertyChanged(nameof(Odds));
        }
    }

    /// <summary>
    /// 中奖
    /// 开奖号码或开奖结果
    /// </summary>
    public string WinningNumbers
    {
        get => _winningNumbers;
        set
        {
            _winningNumbers = value ?? string.Empty;
            OnPropertyChanged(nameof(WinningNumbers));
        }
    }

    /// <summary>
    /// 回水
    /// 回水金额
    /// </summary>
    public decimal RebateAmount
    {
        get => _rebateAmount;
        set
        {
            _rebateAmount = value;
            OnPropertyChanged(nameof(RebateAmount));
        }
    }

    /// <summary>
    /// 实际中奖金额
    /// 仅在中奖时有值，用于准确计算盈亏
    /// </summary>
    public decimal? ActualWinAmount
    {
        get => _actualWinAmount;
        set
        {
            _actualWinAmount = value;
            OnPropertyChanged(nameof(ActualWinAmount));
            OnPropertyChanged(nameof(ProfitLoss)); // 中奖金额变化时重新计算盈亏
        }
    }

    /// <summary>
    /// 结算
    /// 投注结算状态（赢、输、和局等）
    /// </summary>
    public EnumBetOrderStatus SettlementStatus
    {
        get => _settlementStatus;
        set
        {
            _settlementStatus = value;
            OnPropertyChanged(nameof(SettlementStatus));
            OnPropertyChanged(nameof(SettlementStatusText));
        }
    }

    /// <summary>
    /// 结算状态文本
    /// 用于界面显示的结算状态文本
    /// </summary>
    public string SettlementStatusText
    {
        get
        {
            return _settlementStatus switch
            {
                EnumBetOrderStatus.Confirmed => @"待结算",
                EnumBetOrderStatus.Win => @"赢",
                EnumBetOrderStatus.Lose => @"输",
                EnumBetOrderStatus.Draw => @"和局",
                EnumBetOrderStatus.Cancelled => @"已取消",
                _ => @"未知"
            };
        }
    }

    /// <summary>
    /// 时间
    /// 投注时间
    /// </summary>
    public DateTime CreatedTime
    {
        get => _createdTime;
        set
        {
            _createdTime = value;
            OnPropertyChanged(nameof(CreatedTime));
            OnPropertyChanged(nameof(CreatedTimeText));
        }
    }

    /// <summary>
    /// 时间文本
    /// 用于界面显示的时间格式
    /// </summary>
    public string CreatedTimeText => _createdTime.ToString(@"yyyy-MM-dd HH:mm:ss");

    /// <summary>
    /// 订单真假状态
    /// true: 真订单（用户在创建/封盘时为真人状态）
    /// false: 假订单（用户在创建/封盘时为假人状态）
    /// </summary>
    public bool IsRealOrder
    {
        get => _isRealOrder;
        set
        {
            _isRealOrder = value;
            OnPropertyChanged(nameof(IsRealOrder));
            OnPropertyChanged(nameof(OrderTypeText));
        }
    }

    /// <summary>
    /// 订单类型文本
    /// 用于界面显示的订单类型
    /// </summary>
    public string OrderTypeText => _isRealOrder ? "真" : "假";

    /// <summary>
    /// 是否为有效投注
    /// 有效投注是指开奖后赢了或输了的注单，和局的注单不算有效投注
    /// </summary>
    public bool IsValidBet => _settlementStatus == EnumBetOrderStatus.Win || _settlementStatus == EnumBetOrderStatus.Lose;

    /// <summary>
    /// 盈亏金额
    /// 正数表示盈利，负数表示亏损
    /// </summary>
    public decimal ProfitLoss
    {
        get
        {
            return _settlementStatus switch
            {
                EnumBetOrderStatus.Win => (_actualWinAmount ?? 0) - _amount, // 赢：实际中奖金额 - 投注金额
                EnumBetOrderStatus.Lose => -_amount, // 输：-投注金额
                EnumBetOrderStatus.Draw => 0, // 和局：0
                EnumBetOrderStatus.Cancelled => 0, // 取消：0
                _ => 0 // 待结算：0
            };
        }
    }

    /// <summary>
    /// 中奖金额文本
    /// 显示实际的中奖金额（中奖时显示中奖金额，未中奖时显示0，待结算时显示空）
    /// </summary>
    public string WinAmountText
    {
        get
        {
            return _settlementStatus switch
            {
                EnumBetOrderStatus.Win => (_amount * _odds).ToString(@"F2"), // 赢：投注金额 * 赔率
                EnumBetOrderStatus.Lose => @"0.00", // 输：0
                EnumBetOrderStatus.Draw => _amount.ToString(@"F2"), // 和局：退还本金
                EnumBetOrderStatus.Cancelled => _amount.ToString(@"F2"), // 取消：退还本金
                EnumBetOrderStatus.Confirmed => @"", // 待结算：空白
                _ => @"" // 未知：空白
            };
        }
    }

    /// <summary>
    /// 结算金额（盈亏金额）
    /// 显示盈亏金额（中奖金额 - 投注金额）
    /// </summary>
    public string SettlementAmountText
    {
        get
        {
            return _settlementStatus switch
            {
                EnumBetOrderStatus.Win => ProfitLoss.ToString(@"F2"), // 赢：盈亏金额
                EnumBetOrderStatus.Lose => ProfitLoss.ToString(@"F2"), // 输：盈亏金额（负数）
                EnumBetOrderStatus.Draw => @"0.00", // 和局：0
                EnumBetOrderStatus.Cancelled => @"0.00", // 取消：0
                EnumBetOrderStatus.Confirmed => @"", // 待结算：空白
                _ => @"" // 未知：空白
            };
        }
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
