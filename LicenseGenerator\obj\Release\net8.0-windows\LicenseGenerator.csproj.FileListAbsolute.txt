F:\SolutionCommandGuard\LicenseGenerator\bin\Release\net8.0-windows\LicenseGenerator.exe
F:\SolutionCommandGuard\LicenseGenerator\bin\Release\net8.0-windows\LicenseGenerator.deps.json
F:\SolutionCommandGuard\LicenseGenerator\bin\Release\net8.0-windows\LicenseGenerator.runtimeconfig.json
F:\SolutionCommandGuard\LicenseGenerator\bin\Release\net8.0-windows\LicenseGenerator.dll
F:\SolutionCommandGuard\LicenseGenerator\bin\Release\net8.0-windows\LicenseGenerator.pdb
F:\SolutionCommandGuard\LicenseGenerator\bin\Release\net8.0-windows\System.Management.dll
F:\SolutionCommandGuard\LicenseGenerator\bin\Release\net8.0-windows\runtimes\win\lib\net8.0\System.Management.dll
F:\SolutionCommandGuard\LicenseGenerator\bin\Release\net8.0-windows\CommandGuard.Licensing.dll
F:\SolutionCommandGuard\LicenseGenerator\bin\Release\net8.0-windows\CommandGuard.Licensing.pdb
F:\SolutionCommandGuard\LicenseGenerator\obj\Release\net8.0-windows\LicenseGenerator.csproj.AssemblyReference.cache
F:\SolutionCommandGuard\LicenseGenerator\obj\Release\net8.0-windows\LicenseGenerator.FormLicenseGenerator.resources
F:\SolutionCommandGuard\LicenseGenerator\obj\Release\net8.0-windows\LicenseGenerator.csproj.GenerateResource.cache
F:\SolutionCommandGuard\LicenseGenerator\obj\Release\net8.0-windows\LicenseGenerator.GeneratedMSBuildEditorConfig.editorconfig
F:\SolutionCommandGuard\LicenseGenerator\obj\Release\net8.0-windows\LicenseGenerator.AssemblyInfoInputs.cache
F:\SolutionCommandGuard\LicenseGenerator\obj\Release\net8.0-windows\LicenseGenerator.AssemblyInfo.cs
F:\SolutionCommandGuard\LicenseGenerator\obj\Release\net8.0-windows\LicenseGenerator.csproj.CoreCompileInputs.cache
F:\SolutionCommandGuard\LicenseGenerator\obj\Release\net8.0-windows\LicenseG.97897944.Up2Date
F:\SolutionCommandGuard\LicenseGenerator\obj\Release\net8.0-windows\LicenseGenerator.dll
F:\SolutionCommandGuard\LicenseGenerator\obj\Release\net8.0-windows\refint\LicenseGenerator.dll
F:\SolutionCommandGuard\LicenseGenerator\obj\Release\net8.0-windows\LicenseGenerator.pdb
F:\SolutionCommandGuard\LicenseGenerator\obj\Release\net8.0-windows\LicenseGenerator.genruntimeconfig.cache
F:\SolutionCommandGuard\LicenseGenerator\obj\Release\net8.0-windows\ref\LicenseGenerator.dll
