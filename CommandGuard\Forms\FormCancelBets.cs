using System.ComponentModel;
using CommandGuard.Enums;
using CommandGuard.Helpers;
using CommandGuard.Interfaces.Business;
using CommandGuard.Interfaces.Chat;
using CommandGuard.Interfaces.Lottery;
using CommandGuard.Models;
using CommandGuard.Services.Business;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Forms;

/// <summary>
/// 撤单窗口
/// 显示有未结算投注的用户列表，支持批量撤单操作
/// </summary>
public partial class FormCancelBets : Form
{
    #region 字段和属性

    private readonly ILogger<FormCancelBets> _logger;
    private readonly IBetRecordService _betRecordService;
    private readonly IMemberService _memberService;
    private readonly IIssueTimeService _issueTimeService;
    private readonly IChatService _chatService;
    private readonly CancelBetService _cancelBetService;

    /// <summary>
    /// 撤单用户数据列表
    /// </summary>
    private List<CancelBetUserViewModel> _cancelBetUsers = [];

    /// <summary>
    /// 数据绑定源
    /// </summary>
    private readonly BindingSource _bindingSource = new();

    #endregion

    #region 构造函数

    /// <summary>
    /// 构造函数
    /// </summary>
    public FormCancelBets(
        ILogger<FormCancelBets> logger,
        IBetRecordService betRecordService,
        IMemberService memberService,
        IIssueTimeService issueTimeService,
        IChatService chatService,
        CancelBetService cancelBetService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _betRecordService = betRecordService ?? throw new ArgumentNullException(nameof(betRecordService));
        _memberService = memberService ?? throw new ArgumentNullException(nameof(memberService));
        _issueTimeService = issueTimeService ?? throw new ArgumentNullException(nameof(issueTimeService));
        _chatService = chatService ?? throw new ArgumentNullException(nameof(chatService));
        _cancelBetService = cancelBetService ?? throw new ArgumentNullException(nameof(cancelBetService));

        InitializeComponent();
        InitializeDataGridView();
    }

    #endregion

    #region 初始化

    /// <summary>
    /// 初始化DataGridView
    /// </summary>
    private void InitializeDataGridView()
    {
        try
        {
            // 应用投注记录表格的基础样式配置
            ApplyBetRecordTableStyle(dataGridView_CancelBets);

            // 清空现有列
            dataGridView_CancelBets.Columns.Clear();

            // 添加选择列（复选框）
            var checkBoxColumn = new DataGridViewCheckBoxColumn
            {
                Name = @"Selected",
                HeaderText = @"选择",
                DataPropertyName = @"Selected",
                Width = 60,
                ReadOnly = false
            };
            dataGridView_CancelBets.Columns.Add(checkBoxColumn);

            // 添加昵称列
            var nickNameColumn = new DataGridViewTextBoxColumn
            {
                Name = @"NickName",
                HeaderText = @"昵称",
                DataPropertyName = @"NickName",
                Width = 120,
                ReadOnly = true
            };
            dataGridView_CancelBets.Columns.Add(nickNameColumn);

            // 添加账户列
            var accountColumn = new DataGridViewTextBoxColumn
            {
                Name = @"Account",
                HeaderText = @"账户",
                DataPropertyName = @"Account",
                Width = 120,
                ReadOnly = true
            };
            dataGridView_CancelBets.Columns.Add(accountColumn);

            // 添加期数列
            var issuesColumn = new DataGridViewTextBoxColumn
            {
                Name = @"Issues",
                HeaderText = @"期数",
                DataPropertyName = @"IssuesText",
                Width = 200,
                ReadOnly = true
            };
            dataGridView_CancelBets.Columns.Add(issuesColumn);

            // 添加未结算金额列
            var unsettledAmountColumn = new DataGridViewTextBoxColumn
            {
                Name = @"UnsettledAmount",
                HeaderText = @"未结算金额",
                DataPropertyName = @"UnsettledAmount",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = { Format = "F2" }
            };
            dataGridView_CancelBets.Columns.Add(unsettledAmountColumn);

            // 添加订单数量列
            var orderCountColumn = new DataGridViewTextBoxColumn
            {
                Name = @"OrderCount",
                HeaderText = @"订单数量",
                DataPropertyName = @"OrderCount",
                Width = 100,
                ReadOnly = true
            };
            dataGridView_CancelBets.Columns.Add(orderCountColumn);

            // 绑定数据源
            dataGridView_CancelBets.DataSource = _bindingSource;

            // 添加DataError事件处理，防止数据更新时出现异常
            dataGridView_CancelBets.DataError += DataGridView_CancelBets_DataError;

            // 添加CellFormatting事件处理，实现交替行颜色
            dataGridView_CancelBets.CellFormatting += DataGridView_CancelBets_CellFormatting;

            _logger.LogDebug(@"撤单窗口DataGridView初始化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"初始化撤单窗口DataGridView失败");
        }
    }

    /// <summary>
    /// 应用投注记录表格的样式配置
    /// </summary>
    private void ApplyBetRecordTableStyle(DataGridView dataGridView)
    {
        // 基础设置
        dataGridView.AutoGenerateColumns = false;
        dataGridView.AllowUserToAddRows = false;
        dataGridView.AllowUserToDeleteRows = false;
        dataGridView.ReadOnly = false; // 撤单表格需要允许编辑（选择框）
        dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
        dataGridView.MultiSelect = true; // 支持多选
        dataGridView.AllowUserToResizeColumns = false;
        dataGridView.AllowUserToResizeRows = false;
        dataGridView.RowHeadersVisible = false;

        // 滚动条设置
        dataGridView.ScrollBars = ScrollBars.Vertical;
        dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;

        // 表头样式（与投注记录表格一致）
        dataGridView.EnableHeadersVisualStyles = false;
        dataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.LightGray;
        dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.Black;
        dataGridView.ColumnHeadersDefaultCellStyle.Font = new Font(@"Microsoft YaHei", 9F, FontStyle.Bold);
        dataGridView.ColumnHeadersHeight = 24;
        dataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;

        // 行样式
        dataGridView.RowTemplate.Height = 22;
        dataGridView.DefaultCellStyle.Font = new Font(@"Microsoft YaHei", 9F);
        dataGridView.DefaultCellStyle.SelectionBackColor = Color.LightBlue;
        dataGridView.DefaultCellStyle.SelectionForeColor = Color.Black;

        // 网格线样式
        dataGridView.GridColor = Color.LightGray;
        dataGridView.CellBorderStyle = DataGridViewCellBorderStyle.Single;
    }

    #endregion

    #region 数据加载

    /// <summary>
    /// 加载撤单用户数据
    /// </summary>
    public async Task LoadCancelBetUsersAsync()
    {
        try
        {
            _logger.LogInformation(@"开始加载撤单用户数据");

            // 获取所有未结算的投注订单
            var unSettledOrders = await _betRecordService.GetUnSettledBetOrdersAsync();

            if (!unSettledOrders.Any())
            {
                _logger.LogInformation(@"没有找到未结算的投注订单");
                _cancelBetUsers.Clear();
                _bindingSource.DataSource = _cancelBetUsers;
                return;
            }

            // 按用户分组
            var userGroups = unSettledOrders.GroupBy(o => o.Account).ToList();

            // 获取所有相关用户信息
            var allMembers = await _memberService.GetMembersAsync();
            var memberDict = allMembers.ToDictionary(m => m.Account, m => m);

            // 创建撤单用户视图模型
            _cancelBetUsers = [];
            foreach (var userGroup in userGroups)
            {
                var account = userGroup.Key;
                var userOrders = userGroup.ToList();

                // 获取用户信息
                var member = memberDict.GetValueOrDefault(account);
                if (member == null)
                {
                    _logger.LogWarning(@"用户不存在，账号: {Account}", account);
                    continue;
                }

                // 按期号分组
                var issueGroups = userOrders.GroupBy(o => o.Issue).ToList();
                var issues = issueGroups.Select(g => g.Key).OrderBy(i => i).ToList();
                var totalAmount = userOrders.Sum(o => o.Amount);
                var totalCount = userOrders.Count;

                var cancelBetUser = new CancelBetUserViewModel
                {
                    Account = account,
                    NickName = member.NickName,
                    Issues = issues,
                    UnsettledAmount = totalAmount,
                    OrderCount = totalCount,
                    UnSettledOrders = userOrders,
                    Selected = false
                };

                _cancelBetUsers.Add(cancelBetUser);
            }

            // 按账号排序
            _cancelBetUsers = _cancelBetUsers.OrderBy(u => u.Account).ToList();

            // 只在首次加载时绑定数据源，避免重复绑定导致的问题
            if (_bindingSource.DataSource == null)
            {
                _bindingSource.DataSource = _cancelBetUsers;
            }
            else
            {
                // 如果已经绑定，则重置数据源
                _bindingSource.ResetBindings(false);
            }

            _logger.LogInformation(@"撤单用户数据加载完成，共 {Count} 个用户有未结算投注", _cancelBetUsers.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"加载撤单用户数据失败");
            MessageBox.Show($@"加载数据失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// 窗口加载事件
    /// </summary>
    private async void FormCancelBets_Load(object sender, EventArgs e)
    {
        await LoadCancelBetUsersAsync();
    }

    /// <summary>
    /// 刷新按钮点击事件
    /// </summary>
    private async void button_Refresh_Click(object sender, EventArgs e)
    {
        try
        {
            _logger.LogInformation(@"用户点击刷新按钮，重新加载撤单数据");
            await SafeReloadDataAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"刷新撤单数据失败");
            MessageBox.Show($@"刷新数据失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 全选按钮点击事件
    /// </summary>
    private void button_SelectAll_Click(object sender, EventArgs e)
    {
        try
        {
            foreach (var user in _cancelBetUsers)
            {
                user.Selected = true;
            }

            dataGridView_CancelBets.Refresh();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"全选操作失败");
        }
    }

    /// <summary>
    /// 全不选按钮点击事件
    /// </summary>
    private void button_SelectNone_Click(object sender, EventArgs e)
    {
        try
        {
            foreach (var user in _cancelBetUsers)
            {
                user.Selected = false;
            }

            dataGridView_CancelBets.Refresh();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"全不选操作失败");
        }
    }

    /// <summary>
    /// 关闭按钮点击事件
    /// </summary>
    private void button_Close_Click(object sender, EventArgs e)
    {
        Close();
    }

    /// <summary>
    /// 撤单并退款按钮点击事件
    /// </summary>
    private async void button_CancelAndRefund_Click(object sender, EventArgs e)
    {
        try
        {
            // 获取选中的用户
            var selectedUsers = _cancelBetUsers.Where(u => u.Selected).ToList();

            if (!selectedUsers.Any())
            {
                MessageBox.Show(@"请先选择要撤单的用户！", @"提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 确认操作
            var totalOrders = selectedUsers.Sum(u => u.OrderCount);
            var totalAmount = selectedUsers.Sum(u => u.UnsettledAmount);

            var confirmResult = MessageBox.Show(
                $@"确定要为选中的 {selectedUsers.Count} 个用户撤销投注吗？" + Environment.NewLine +
                $@"总计 {totalOrders} 个订单，退还积分 {totalAmount:F2} 。" + Environment.NewLine +
                @"操作不可撤销，请确认！",
                @"撤单确认",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2);

            if (confirmResult != DialogResult.Yes)
            {
                return;
            }

            // 执行撤单操作
            await ExecuteCancelBetsAsync(selectedUsers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"撤单操作失败");
            MessageBox.Show($@"撤单操作失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// DataGridView数据错误事件处理
    /// </summary>
    private void DataGridView_CancelBets_DataError(object? sender, DataGridViewDataErrorEventArgs e)
    {
        try
        {
            // 记录错误但不显示给用户，避免在数据更新时出现异常对话框
            _logger.LogWarning(@"DataGridView数据错误: {Error}, 行: {RowIndex}, 列: {ColumnIndex}",
                e.Exception?.Message, e.RowIndex, e.ColumnIndex);

            // 标记异常已处理
            e.ThrowException = false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"处理DataGridView数据错误时发生异常");
        }
    }

    /// <summary>
    /// DataGridView单元格格式化事件处理
    /// 实现与投注记录表格相同的交替行颜色样式
    /// </summary>
    private void DataGridView_CancelBets_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
    {
        try
        {
            if (sender is not DataGridView grid || e.RowIndex < 0 || e.ColumnIndex < 0)
                return;

            // 设置行的背景色（交替行颜色，与投注记录表格一致）
            if (e.CellStyle != null)
            {
                if (e.RowIndex % 2 == 0)
                {
                    e.CellStyle.BackColor = Color.White;
                }
                else
                {
                    e.CellStyle.BackColor = Color.AliceBlue;
                }

                // 设置默认前景色
                e.CellStyle.ForeColor = Color.Black;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"撤单表格格式化失败，行: {Row}, 列: {Column}", e.RowIndex, e.ColumnIndex);
        }
    }

    #endregion

    #region 撤单操作

    /// <summary>
    /// 执行撤单操作
    /// </summary>
    private async Task ExecuteCancelBetsAsync(List<CancelBetUserViewModel> selectedUsers)
    {
        try
        {
            // 禁用按钮
            button_CancelAndRefund.Enabled = false;
            button_CancelAndRefund.Text = @"正在撤单...";

            // 提取用户账号列表
            var userAccounts = selectedUsers.Select(u => u.Account).ToList();

            // 使用公共的撤销投注服务
            var result = await _cancelBetService.CancelBetsByAccountsAsync(userAccounts, @"管理员", true);

            // 显示结果
            if (result.Success)
            {
                var resultMessage = $@"撤单操作完成！" + Environment.NewLine +
                                    $@"撤销订单数: {result.CancelledOrderCount}" + Environment.NewLine +
                                    $@"退款总额: {result.TotalRefundAmount:F2}" + Environment.NewLine +
                                    $@"涉及用户: {result.AffectedUsers.Count}个";

                MessageBox.Show(resultMessage, @"撤单成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show($@"撤单操作失败：{result.Message}", @"撤单失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            // 安全地重新加载数据
            await SafeReloadDataAsync();
        }
        finally
        {
            // 恢复按钮状态
            button_CancelAndRefund.Enabled = true;
            button_CancelAndRefund.Text = @"撤单并退款";
        }
    }

    /// <summary>
    /// 安全地重新加载数据，避免DataGridView索引错误
    /// </summary>
    private async Task SafeReloadDataAsync()
    {
        try
        {
            // 1. 先清空数据源，避免索引冲突
            _bindingSource.DataSource = null;
            dataGridView_CancelBets.DataSource = null;

            // 2. 清空内存中的数据
            _cancelBetUsers.Clear();

            // 3. 强制刷新界面
            dataGridView_CancelBets.Refresh();
            Application.DoEvents();

            // 4. 重新加载数据
            await LoadCancelBetUsersAsync();

            // 5. 重新绑定数据源
            _bindingSource.DataSource = _cancelBetUsers;
            dataGridView_CancelBets.DataSource = _bindingSource;

            // 6. 刷新显示
            dataGridView_CancelBets.Refresh();

            _logger.LogDebug(@"撤单数据安全重新加载完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"安全重新加载撤单数据失败");

            // 如果重新加载失败，至少确保界面是干净的
            try
            {
                _cancelBetUsers.Clear();
                _bindingSource.DataSource = _cancelBetUsers;
                dataGridView_CancelBets.Refresh();
            }
            catch (Exception innerEx)
            {
                _logger.LogError(innerEx, @"清理撤单界面失败");
            }
        }
    }



    #endregion
}

/// <summary>
/// 撤单用户视图模型
/// </summary>
public class CancelBetUserViewModel : INotifyPropertyChanged
{
    private bool _selected;

    /// <summary>
    /// 是否选中
    /// </summary>
    public bool Selected
    {
        get => _selected;
        set
        {
            _selected = value;
            OnPropertyChanged(nameof(Selected));
        }
    }

    /// <summary>
    /// 用户账号
    /// </summary>
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 用户昵称
    /// </summary>
    public string NickName { get; set; } = string.Empty;

    /// <summary>
    /// 未结算的期号列表
    /// </summary>
    public List<string> Issues { get; set; } = [];

    /// <summary>
    /// 期号文本（用于显示）
    /// </summary>
    public string IssuesText => string.Join(", ", Issues);

    /// <summary>
    /// 未结算金额
    /// </summary>
    public decimal UnsettledAmount { get; set; }

    /// <summary>
    /// 订单数量
    /// </summary>
    public int OrderCount { get; set; }

    /// <summary>
    /// 未结算订单列表
    /// </summary>
    public List<BetOrder> UnSettledOrders { get; set; } = [];

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}