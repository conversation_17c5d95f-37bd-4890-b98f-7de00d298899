using CommandGuard.Configuration;
using CommandGuard.Interfaces.Infrastructure;
using CommandGuard.Interfaces.Lottery;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Infrastructure;

/// <summary>
/// 系统管理服务实现
/// </summary>
public class SystemManagementService(
    IFreeSql fSql,
    ILogger<SystemManagementService> logger,
    IOddsService oddsService,
    ISystemSettingService systemSettingService)
    : ISystemManagementService
{
    /// <summary>
    /// 清除一切记录 - 系统数据重置功能
    ///
    /// 功能：
    /// - 清除所有业务数据表的记录
    /// - 保留系统配置和基础数据
    /// - 记录操作日志和操作人
    /// - 支持事务回滚
    ///
    /// 清除范围：
    /// - 投注记录、开奖记录
    /// - 用户财务记录
    /// - 消息记录
    /// - 其他业务数据
    ///
    /// ⚠️ 警告：这是危险操作，会清除所有业务数据
    /// </summary>
    public async Task<bool> ClearAllRecordsAsync(string operatorName)
    {
        try
        {
            logger.LogWarning(@"开始清除一切记录操作，操作人: {Operator}", operatorName);

            logger.LogInformation(@"开始清除数据库中的所有记录");

            // 1. 清除投注记录（最重要的业务数据）
            var deletedBetOrders = await fSql.Delete<BetOrder>().Where("1=1").ExecuteAffrowsAsync();
            logger.LogInformation(@"已清除投注记录，删除 {Count} 条记录", deletedBetOrders);

            // 2. 清除开奖记录
            var deletedKaiJiang = await fSql.Delete<KaiJiang>().Where("1=1").ExecuteAffrowsAsync();
            logger.LogInformation(@"已清除开奖记录，删除 {Count} 条记录", deletedKaiJiang);

            // 3. 清除期号时间记录
            var deletedIssueTime = await fSql.Delete<IssueTime>().Where("1=1").ExecuteAffrowsAsync();
            logger.LogInformation(@"已清除期号时间记录，删除 {Count} 条记录", deletedIssueTime);

            // 4. 清除上下分记录
            var deletedDepositRequests = await fSql.Delete<DepositRequest>().Where("1=1").ExecuteAffrowsAsync();
            var deletedWithdrawRequests = await fSql.Delete<WithdrawRequest>().Where("1=1").ExecuteAffrowsAsync();
            logger.LogInformation(@"已清除上下分记录，删除上分申请 {DepositCount} 条，下分申请 {WithdrawCount} 条",
                deletedDepositRequests, deletedWithdrawRequests);

            // 5. 清除财务记录
            var deletedFinancialRecords = await fSql.Delete<FinancialRecord>().Where("1=1").ExecuteAffrowsAsync();
            logger.LogInformation(@"已清除财务记录，删除 {Count} 条记录", deletedFinancialRecords);

            // 6. 清除消息记录
            var deletedMessages = await fSql.Delete<InternalMessage>().Where("1=1").ExecuteAffrowsAsync();
            logger.LogInformation(@"已清除消息记录，删除 {Count} 条记录", deletedMessages);

            // 7. 清除会员信息（保留管理员账户）
            var deletedMembers = await fSql.Delete<Member>()
                .Where(m => m.Account != @"admin" && m.Account != @"administrator")
                .ExecuteAffrowsAsync();
            logger.LogInformation(@"已清除会员信息，删除 {Count} 个会员", deletedMembers);

            // 8. 重置剩余会员的余额信息
            await fSql.Update<Member>()
                .Set(m => m.Balance, 0)
                .ExecuteAffrowsAsync();
            logger.LogInformation(@"已重置剩余会员的余额信息");

            // 9. 清除日志文件
            var logsClearSuccess = await ClearLogFilesAsync();
            if (logsClearSuccess)
            {
                logger.LogInformation(@"已清除日志文件");
            }
            else
            {
                logger.LogWarning(@"清除日志文件失败，但继续其他操作");
            }

            logger.LogWarning(@"清除一切记录操作完成，操作人: {Operator}", operatorName);
            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"清除一切记录操作异常，操作人: {Operator}", operatorName);
            return false;
        }
    }

    /// <summary>
    /// 初始化出厂设置 - 系统恢复默认配置
    ///
    /// 功能：
    /// - 清除所有业务数据
    /// - 重置系统设置为默认值
    /// - 重置赔率配置为默认值
    /// - 记录操作日志
    ///
    /// 操作步骤：
    /// 1. 清除所有记录
    /// 2. 初始化默认系统设置
    /// 3. 初始化默认赔率配置
    /// 4. 记录操作日志
    ///
    /// 用途：系统重置、故障恢复、新系统部署
    /// </summary>
    public async Task<bool> InitializeFactorySettingsAsync(string operatorName)
    {
        try
        {
            logger.LogWarning(@"开始初始化出厂设置操作，操作人: {Operator}", operatorName);

            // 1. 先执行清除一切记录
            var clearSuccess = await ClearAllRecordsAsync(operatorName);
            if (!clearSuccess)
            {
                logger.LogError(@"清除记录失败，终止出厂设置初始化");
                return false;
            }

            // 2. 重置返点配置为出厂默认值
            var memberRebateSuccess = await systemSettingService.UpdateMemberRebatePercentAsync(1.5m);

            if (!memberRebateSuccess)
            {
                logger.LogWarning(@"重置返点配置失败，但继续其他设置");
            }
            else
            {
                logger.LogInformation(@"已重置返点配置为出厂默认值");
            }

            // 3. 重置所有运行时配置为出厂默认值
            RuntimeConfiguration.ResetToFactoryDefaults();
            logger.LogInformation(@"已重置所有运行时配置为出厂默认值");

            // 4. 重置赔率配置为出厂默认值
            try
            {
                var oddsResetSuccess = await oddsService.ResetToFactoryDefaultOddsAsync();
                if (oddsResetSuccess)
                {
                    logger.LogInformation(@"已重置赔率配置为出厂默认值");
                }
                else
                {
                    logger.LogWarning(@"重置赔率配置失败，但继续其他操作");
                }
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, @"重置赔率配置失败，但继续其他操作");
            }

            // 5. 重置系统设置为出厂默认值
            try
            {
                var settingsResetSuccess = await systemSettingService.ResetToFactoryDefaultSettingsAsync();
                if (settingsResetSuccess)
                {
                    logger.LogInformation(@"已重置系统设置为出厂默认值");
                }
                else
                {
                    logger.LogWarning(@"重置系统设置失败，但继续其他操作");
                }
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, @"重置系统设置失败，但继续其他操作");
            }

            // 6. 清除日志文件（出厂设置也应该清除日志）
            var logsClearSuccess = await ClearLogFilesAsync();
            if (logsClearSuccess)
            {
                logger.LogInformation(@"已清除日志文件");
            }
            else
            {
                logger.LogWarning(@"清除日志文件失败，但继续其他操作");
            }

            // 7. 这里可以添加其他系统参数的重置逻辑
            // 例如：重置开盘封盘时间、重置其他配置表等

            logger.LogWarning(@"初始化出厂设置操作完成，操作人: {Operator}", operatorName);
            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"初始化出厂设置操作异常，操作人: {Operator}", operatorName);
            return false;
        }
    }

    /// <summary>
    /// 清除日志文件 - 智能日志清理功能
    ///
    /// 功能：
    /// - 递归扫描logs目录下的所有文件
    /// - 检测文件占用状态，跳过正在使用的文件
    /// - 删除可删除的日志文件
    /// - 清理删除后的空目录
    /// - 详细的操作统计和日志记录
    ///
    /// 安全机制：
    /// - 不强制删除被占用的文件
    /// - 异常处理确保单个文件失败不影响整体
    /// - 返回操作结果统计
    /// </summary>
    private Task<bool> ClearLogFilesAsync()
    {
        try
        {
            logger.LogInformation(@"开始清除日志文件");

            // 获取应用程序根目录
            var appDirectory = AppContext.BaseDirectory;
            var logsDirectory = Path.Combine(appDirectory, "logs");

            // 检查logs目录是否存在
            if (!Directory.Exists(logsDirectory))
            {
                logger.LogInformation(@"logs目录不存在，无需清除");
                return Task.FromResult(true);
            }

            // 获取logs目录中的所有文件
            var logFiles = Directory.GetFiles(logsDirectory, "*", SearchOption.AllDirectories);

            if (logFiles.Length == 0)
            {
                logger.LogInformation(@"logs目录为空，无需清除");
                return Task.FromResult(true);
            }

            logger.LogInformation(@"发现 {Count} 个日志文件，开始删除", logFiles.Length);

            var deletedCount = 0;
            var failedCount = 0;

            // 逐个删除日志文件
            foreach (var logFile in logFiles)
            {
                try
                {
                    // 检查文件是否被占用
                    if (IsFileInUse(logFile))
                    {
                        logger.LogWarning(@"日志文件被占用，跳过删除: {FilePath}", logFile);
                        failedCount++;
                        continue;
                    }

                    File.Delete(logFile);
                    deletedCount++;
                    logger.LogDebug(@"已删除日志文件: {FilePath}", logFile);
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, @"删除日志文件失败: {FilePath}", logFile);
                    failedCount++;
                }
            }

            // 尝试删除空的子目录
            try
            {
                var subDirectories = Directory.GetDirectories(logsDirectory, "*", SearchOption.AllDirectories);
                foreach (var subDir in subDirectories.OrderByDescending(d => d.Length)) // 从最深层开始删除
                {
                    try
                    {
                        if (Directory.GetFiles(subDir).Length == 0 && Directory.GetDirectories(subDir).Length == 0)
                        {
                            Directory.Delete(subDir);
                            logger.LogDebug(@"已删除空目录: {DirPath}", subDir);
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogDebug(ex, @"删除空目录失败: {DirPath}", subDir);
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogDebug(ex, @"清理空目录时发生异常");
            }

            logger.LogInformation(@"日志文件清除完成，成功删除: {DeletedCount}, 失败: {FailedCount}", deletedCount, failedCount);

            // 如果有文件删除成功，就认为操作成功
            return Task.FromResult(deletedCount > 0 || logFiles.Length == 0);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"清除日志文件时发生异常");
            return Task.FromResult(false);
        }
    }

    /// <summary>
    /// 检查文件是否被占用 - 安全删除前的检查
    ///
    /// 功能：
    /// - 尝试以独占模式打开文件
    /// - 检测文件是否被其他进程占用
    /// - 避免删除正在使用的文件
    ///
    /// 检测原理：
    /// - 以FileShare.None模式打开文件
    /// - 如果成功则文件未被占用
    /// - 如果IOException则文件被占用
    ///
    /// 返回值：true=被占用，false=可删除
    /// </summary>
    private static bool IsFileInUse(string filePath)
    {
        try
        {
            using var stream = File.Open(filePath, FileMode.Open, FileAccess.Read, FileShare.None);
            return false;
        }
        catch (IOException)
        {
            return true;
        }
        catch
        {
            return false;
        }
    }


}