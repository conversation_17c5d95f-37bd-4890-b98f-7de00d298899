using CommandGuard.Enums;
using CommandGuard.Models;

namespace CommandGuard.Interfaces.Chat;

/// <summary>
/// 消息存储服务接口
/// 负责将转换后的内部消息存储到数据库
/// 提供消息的增删改查和状态管理功能
/// </summary>
public interface IMessageStorageService
{
    /// <summary>
    /// 保存单条内部消息到数据库
    /// </summary>
    /// <param name="message">要保存的内部消息</param>
    /// <returns>保存后的消息ID</returns>
    /// <exception cref="ArgumentNullException">当消息为null时抛出</exception>
    /// <exception cref="ArgumentException">当消息数据无效时抛出</exception>
    Task<long> SaveMessageAsync(InternalMessage message);
    
    /// <summary>
    /// 获取指定状态的消息列表
    /// </summary>
    /// <param name="status">消息状态</param>
    /// <returns>消息列表</returns>
    Task<List<InternalMessage>> GetMessagesByStatusAsync(EnumMessageStatus status);

    /// <summary>
    /// 更新消息状态
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <param name="status">新状态</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateMessageStatusAsync(string messageId, EnumMessageStatus status);
}