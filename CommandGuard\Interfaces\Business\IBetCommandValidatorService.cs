namespace CommandGuard.Interfaces.Business;

/// <summary>
/// 投注指令验证服务接口
/// 专门负责投注指令的解析和验证
/// </summary>
public interface IBetCommandValidatorService
{
    /// <summary>
    /// 验证投注指令
    /// </summary>
    /// <param name="command">投注指令</param>
    /// <returns>验证结果：(是否有效, 投注项目, 投注金额, 错误信息)</returns>
    Task<(bool IsValid, string PlayItem, decimal Amount, string ErrorMessage)> ValidateBetCommandAsync(string command);
}
