﻿namespace CommandGuard.Constants;

public static class ImageConstants
{
    /// <summary>
    /// 开奖数据图片保存路径
    /// </summary>
    public static string DrawImagePath { get; set; } = $"{AppDomain.CurrentDomain.BaseDirectory}/images/draw.jpeg";

    /// <summary>
    /// 7行路子图保存路径
    /// </summary>
    public static string TanRows7ImagePath { get; set; } = $"{AppDomain.CurrentDomain.BaseDirectory}/images/tan7.jpeg";

    /// <summary>
    /// 6行路子图保存路径
    /// </summary>
    public static string TanRows6ImagePath { get; set; } = $"{AppDomain.CurrentDomain.BaseDirectory}/images/tan6.jpeg";

    /// <summary>
    /// 7行路子图保存路径（备用）
    /// </summary>
    public static string TanRows77ImagePath { get; set; } = $"{AppDomain.CurrentDomain.BaseDirectory}/images/tan77.jpeg";

    /// <summary>
    /// 6行路子图保存路径（备用）
    /// </summary>
    public static string TanRows66ImagePath { get; set; } = $"{AppDomain.CurrentDomain.BaseDirectory}/images/tan66.jpeg";

    /// <summary>
    /// 图片资源路径缓存字典
    /// 预加载所有需要使用的图片文件路径，避免重复查找文件
    /// </summary>
    public static Dictionary<string, string> ImagePaths { get; } = new()
    {
        ["logo"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "logo.png")[0], // Logo图标
        ["titleBg"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "titleBg.png")[0], // 标题背景图
        ["title"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "title.png")[0], // 标题
        ["detail"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "detail.png")[0], // 详情行背景图
        ["11"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "11.png")[0], // 番摊结果1图标
        ["22"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "22.png")[0], // 番摊结果2图标
        ["33"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "33.png")[0], // 番摊结果3图标
        ["44"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "44.png")[0], // 番摊结果4图标
        ["yellow"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "yellow.png")[0], // 黄色号码背景图
        ["0"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "0.png")[0], // 路子图数字0图标
        ["1"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "1.png")[0], // 路子图数字1图标
        ["2"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "2.png")[0], // 路子图数字2图标
        ["3"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "3.png")[0], // 路子图数字3图标
        ["4"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "4.png")[0], // 路子图数字4图标
        ["new0"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "new0.png")[0], // 路子图数字0图标
        ["new1"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "new1.png")[0], // 路子图数字1图标
        ["new2"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "new2.png")[0], // 路子图数字2图标
        ["new3"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "new3.png")[0], // 路子图数字3图标
        ["new4"] = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory + "/img/", "new4.png")[0] // 路子图数字4图标
    };
}