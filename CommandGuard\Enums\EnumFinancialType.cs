namespace CommandGuard.Enums;

/// <summary>
/// 财务交易类型枚举
/// </summary>
public enum EnumFinancialType
{
    /// <summary>
    /// 上分（增加余额）
    /// </summary>
    Deposit = 1,

    /// <summary>
    /// 下分（减少余额）
    /// </summary>
    Withdraw = 2,

    /// <summary>
    /// 回水（返点奖励）
    /// </summary>
    Rebate = 3,

    /// <summary>
    /// 冻结金额
    /// </summary>
    Freeze = 4,

    /// <summary>
    /// 解冻金额
    /// </summary>
    Unfreeze = 5,

    /// <summary>
    /// 管理员调整
    /// </summary>
    AdminAdjustment = 6
}
