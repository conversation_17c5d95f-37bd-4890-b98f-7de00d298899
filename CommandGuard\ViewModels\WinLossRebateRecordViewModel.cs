using System.ComponentModel;

namespace CommandGuard.ViewModels;

/// <summary>
/// 输赢流水回水记录视图模型
/// 用于在输赢流水回水记录查询界面显示用户的输赢、流水和回水数据
/// </summary>
public class WinLossRebateRecordViewModel : INotifyPropertyChanged
{
    private string _nickName = string.Empty;
    private string _account = string.Empty;
    private decimal _realProfitLoss;
    private decimal _fakeProfitLoss;
    private decimal _totalProfitLoss;
    private decimal _realTurnover;
    private decimal _fakeTurnover;
    private decimal _totalTurnover;
    private decimal _pendingRebate;
    private decimal _rebatePercent;
    private decimal _paidRebate;
    private decimal _totalRebate;

    /// <summary>
    /// 昵称
    /// 用户昵称
    /// </summary>
    public string NickName
    {
        get => _nickName;
        set
        {
            _nickName = value ?? string.Empty;
            OnPropertyChanged(nameof(NickName));
        }
    }

    /// <summary>
    /// 账号
    /// 用户账号
    /// </summary>
    public string Account
    {
        get => _account;
        set
        {
            _account = value ?? string.Empty;
            OnPropertyChanged(nameof(Account));
        }
    }

    /// <summary>
    /// 真盈亏
    /// 真人投注的盈亏金额（正数为盈利，负数为亏损）
    /// </summary>
    public decimal RealProfitLoss
    {
        get => _realProfitLoss;
        set
        {
            _realProfitLoss = value;
            OnPropertyChanged(nameof(RealProfitLoss));
            OnPropertyChanged(nameof(TotalProfitLoss));
        }
    }

    /// <summary>
    /// 假盈亏
    /// 假人投注的盈亏金额（正数为盈利，负数为亏损）
    /// </summary>
    public decimal FakeProfitLoss
    {
        get => _fakeProfitLoss;
        set
        {
            _fakeProfitLoss = value;
            OnPropertyChanged(nameof(FakeProfitLoss));
            OnPropertyChanged(nameof(TotalProfitLoss));
        }
    }

    /// <summary>
    /// 总盈亏
    /// 真盈亏 + 假盈亏的总和
    /// </summary>
    public decimal TotalProfitLoss
    {
        get => _realProfitLoss + _fakeProfitLoss;
        set
        {
            _totalProfitLoss = value;
            OnPropertyChanged(nameof(TotalProfitLoss));
        }
    }

    /// <summary>
    /// 真流水
    /// 真人投注的有效流水金额
    /// </summary>
    public decimal RealTurnover
    {
        get => _realTurnover;
        set
        {
            _realTurnover = value;
            OnPropertyChanged(nameof(RealTurnover));
            OnPropertyChanged(nameof(TotalTurnover));
        }
    }

    /// <summary>
    /// 假流水
    /// 假人投注的有效流水金额
    /// </summary>
    public decimal FakeTurnover
    {
        get => _fakeTurnover;
        set
        {
            _fakeTurnover = value;
            OnPropertyChanged(nameof(FakeTurnover));
            OnPropertyChanged(nameof(TotalTurnover));
        }
    }

    /// <summary>
    /// 总流水
    /// 真流水 + 假流水的总和
    /// </summary>
    public decimal TotalTurnover
    {
        get => _realTurnover + _fakeTurnover;
        set
        {
            _totalTurnover = value;
            OnPropertyChanged(nameof(TotalTurnover));
        }
    }

    /// <summary>
    /// 未回水
    /// 尚未发放的回水金额
    /// </summary>
    public decimal PendingRebate
    {
        get => _pendingRebate;
        set
        {
            _pendingRebate = value;
            OnPropertyChanged(nameof(PendingRebate));
            OnPropertyChanged(nameof(TotalRebate));
        }
    }

    /// <summary>
    /// 回水比例
    /// 回水百分比（如5.5表示5.5%）
    /// </summary>
    public decimal RebatePercent
    {
        get => _rebatePercent;
        set
        {
            _rebatePercent = value;
            OnPropertyChanged(nameof(RebatePercent));
        }
    }

    /// <summary>
    /// 已回水
    /// 已经发放的回水金额
    /// </summary>
    public decimal PaidRebate
    {
        get => _paidRebate;
        set
        {
            _paidRebate = value;
            OnPropertyChanged(nameof(PaidRebate));
            OnPropertyChanged(nameof(TotalRebate));
        }
    }

    /// <summary>
    /// 总回水
    /// 未回水 + 已回水的总和
    /// </summary>
    public decimal TotalRebate
    {
        get => _pendingRebate + _paidRebate;
        set
        {
            _totalRebate = value;
            OnPropertyChanged(nameof(TotalRebate));
        }
    }

    /// <summary>
    /// 净盈亏
    /// 总盈亏 - 总回水（考虑回水后的实际盈亏）
    /// </summary>
    public decimal NetProfitLoss => TotalProfitLoss - TotalRebate;

    /// <summary>
    /// 回水完成率
    /// 已回水 / 总回水的百分比
    /// </summary>
    public decimal RebateCompletionRate
    {
        get
        {
            if (TotalRebate == 0) return 0;
            return Math.Round(PaidRebate / TotalRebate * 100, 2);
        }
    }

    /// <summary>
    /// 是否有未回水
    /// </summary>
    public bool HasPendingRebate => PendingRebate > 0;

    /// <summary>
    /// 是否为盈利用户
    /// </summary>
    public bool IsProfitable => TotalProfitLoss > 0;

    /// <summary>
    /// 流水效率
    /// 总盈亏 / 总流水的比率，反映投注效率
    /// </summary>
    public decimal TurnoverEfficiency
    {
        get
        {
            if (TotalTurnover == 0) return 0;
            return Math.Round(Math.Abs(TotalProfitLoss) / TotalTurnover * 100, 2);
        }
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
