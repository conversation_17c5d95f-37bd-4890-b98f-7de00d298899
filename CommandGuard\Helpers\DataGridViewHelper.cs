namespace CommandGuard.Helpers;

/// <summary>
/// DataGridView配置帮助类
/// 提供统一的DataGridView样式配置和列定义方法
/// 消除重复代码，提高代码复用性和维护性
/// </summary>
public static class DataGridViewHelper
{
    #region 基础配置

    /// <summary>
    /// 应用基础配置到DataGridView
    /// </summary>
    /// <param name="dataGridView">要配置的DataGridView</param>
    private static void ApplyBaseConfiguration(DataGridView dataGridView)
    {
        if (dataGridView == null) throw new ArgumentNullException(nameof(dataGridView));

        // 基础设置
        dataGridView.AutoGenerateColumns = false;
        dataGridView.AllowUserToAddRows = false;
        dataGridView.AllowUserToDeleteRows = false;
        dataGridView.ReadOnly = true;
        dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
        dataGridView.MultiSelect = false;
        dataGridView.AllowUserToResizeColumns = false;
        dataGridView.AllowUserToResizeRows = false;
        dataGridView.RowHeadersVisible = false;

        // 滚动条设置
        dataGridView.ScrollBars = ScrollBars.Vertical;
        dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;

        // 表头样式
        dataGridView.EnableHeadersVisualStyles = false;
        dataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.LightGray;
        dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.Black;
        dataGridView.ColumnHeadersDefaultCellStyle.Font = new Font(@"Microsoft YaHei", 9F, FontStyle.Bold);
        dataGridView.ColumnHeadersHeight = 24;
        dataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;

        // 清空现有列
        dataGridView.Columns.Clear();
    }

    #endregion

    #region 列创建方法

    /// <summary>
    /// 创建文本列
    /// </summary>
    /// <param name="name">列名</param>
    /// <param name="headerText">表头文本</param>
    /// <param name="dataPropertyName">数据属性名</param>
    /// <param name="width">列宽</param>
    /// <param name="format">格式化字符串</param>
    /// <returns>配置好的文本列</returns>
    public static DataGridViewTextBoxColumn CreateTextColumn(
        string name, 
        string headerText, 
        string dataPropertyName, 
        int width, 
        string? format = null)
    {
        var column = new DataGridViewTextBoxColumn
        {
            Name = name,
            HeaderText = headerText,
            DataPropertyName = dataPropertyName,
            Width = width,
            ReadOnly = true
        };

        if (!string.IsNullOrEmpty(format))
        {
            column.DefaultCellStyle = new DataGridViewCellStyle { Format = format };
        }

        return column;
    }

    /// <summary>
    /// 创建按钮列
    /// </summary>
    /// <param name="name">列名</param>
    /// <param name="headerText">表头文本</param>
    /// <param name="buttonText">按钮文本</param>
    /// <param name="width">列宽</param>
    /// <param name="backColor">背景色</param>
    /// <param name="foreColor">前景色</param>
    /// <returns>配置好的按钮列</returns>
    public static DataGridViewButtonColumn CreateButtonColumn(
        string name,
        string headerText,
        string buttonText,
        int width,
        Color? backColor = null,
        Color? foreColor = null)
    {
        var column = new DataGridViewButtonColumn
        {
            Name = name,
            HeaderText = headerText,
            Text = buttonText,
            UseColumnTextForButtonValue = true,
            Width = width
        };

        if (backColor.HasValue || foreColor.HasValue)
        {
            column.DefaultCellStyle = new DataGridViewCellStyle
            {
                BackColor = backColor ?? Color.White,
                ForeColor = foreColor ?? Color.Black,
                SelectionBackColor = backColor ?? Color.White,
                SelectionForeColor = foreColor ?? Color.Black
            };
        }

        return column;
    }

    #endregion

    #region 预定义配置

    /// <summary>
    /// 配置会员数据表格
    /// </summary>
    /// <param name="dataGridView">要配置的DataGridView</param>
    public static void ConfigureMemberDataGridView(DataGridView dataGridView)
    {
        ApplyBaseConfiguration(dataGridView);

        // 修改为单元格选择模式
        dataGridView.SelectionMode = DataGridViewSelectionMode.CellSelect;

        // 添加列（超紧凑布局，进一步缩小总宽度）
        dataGridView.Columns.Add(CreateTextColumn("NickName", "昵称", "NickName", 75));
        dataGridView.Columns.Add(CreateTextColumn("Account", "账号", "Account", 75));
        dataGridView.Columns.Add(CreateTextColumn("Balance", "积分", "Balance", 85, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("UnsettledAmount", "未结算积分", "UnsettledAmount", 80, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("LastIssueProfitLoss", "上期盈亏", "LastIssueProfitLoss", 70, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("RebatePercent", "回水比例", "RebatePercent", 65, "F1"));
        dataGridView.Columns.Add(CreateTextColumn("UserType", "真假人", "UserType", 55));
        dataGridView.Columns.Add(CreateButtonColumn("DeleteButton", "删除", "删除", 50));

        // 设置列对齐方式
        dataGridView.Columns["NickName"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
        dataGridView.Columns["Account"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
        dataGridView.Columns["Balance"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["UnsettledAmount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["LastIssueProfitLoss"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["RebatePercent"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
        dataGridView.Columns["UserType"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

        // 设置最小列宽（超紧凑配置）
        dataGridView.Columns["NickName"].MinimumWidth = 65;
        dataGridView.Columns["Account"].MinimumWidth = 65;
        dataGridView.Columns["Balance"].MinimumWidth = 55;
        dataGridView.Columns["UnsettledAmount"].MinimumWidth = 70;
        dataGridView.Columns["LastIssueProfitLoss"].MinimumWidth = 60;
        dataGridView.Columns["RebatePercent"].MinimumWidth = 55;
        dataGridView.Columns["UserType"].MinimumWidth = 45;
        dataGridView.Columns["DeleteButton"].MinimumWidth = 45;

        // 设置固定列宽，确保紧凑布局（超紧凑模式）
        dataGridView.Columns["NickName"].Width = 75;
        dataGridView.Columns["Account"].Width = 75;
        dataGridView.Columns["Balance"].Width = 85;
        dataGridView.Columns["UnsettledAmount"].Width = 80;
        dataGridView.Columns["LastIssueProfitLoss"].Width = 70;
        dataGridView.Columns["RebatePercent"].Width = 65;
        dataGridView.Columns["UserType"].Width = 55;
        dataGridView.Columns["DeleteButton"].Width = 50;

        // 确保使用固定列宽模式（防止被其他地方重新设置）
        dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
    }

    /// <summary>
    /// 配置上分申请表格
    /// </summary>
    /// <param name="dataGridView">要配置的DataGridView</param>
    public static void ConfigureDepositRequestDataGridView(DataGridView dataGridView)
    {
        ApplyBaseConfiguration(dataGridView);

        // 修改为单元格选择模式
        dataGridView.SelectionMode = DataGridViewSelectionMode.CellSelect;

        // 添加列
        dataGridView.Columns.Add(CreateTextColumn("Account", "账号", "Account", 80));
        dataGridView.Columns.Add(CreateTextColumn("NickName", "昵称", "NickName", 100));
        dataGridView.Columns.Add(CreateTextColumn("Amount", "金额", "Amount", 70, "F2"));
        dataGridView.Columns.Add(CreateButtonColumn("ApproveButton", "操作", "接受上分", 70, Color.LightGreen, Color.Black));
        dataGridView.Columns.Add(CreateButtonColumn("RejectButton", "", "拒绝上分", 70, Color.LightCoral, Color.Black));
    }

    /// <summary>
    /// 配置下分申请表格
    /// </summary>
    /// <param name="dataGridView">要配置的DataGridView</param>
    public static void ConfigureWithdrawRequestDataGridView(DataGridView dataGridView)
    {
        ApplyBaseConfiguration(dataGridView);

        // 修改为单元格选择模式
        dataGridView.SelectionMode = DataGridViewSelectionMode.CellSelect;

        // 添加列
        dataGridView.Columns.Add(CreateTextColumn("Account", "账号", "Account", 80));
        dataGridView.Columns.Add(CreateTextColumn("NickName", "昵称", "NickName", 100));
        dataGridView.Columns.Add(CreateTextColumn("Amount", "金额", "Amount", 70, "F2"));
        dataGridView.Columns.Add(CreateButtonColumn("ApproveButton", "操作", "接受下分", 70, Color.LightBlue, Color.Black));
        dataGridView.Columns.Add(CreateButtonColumn("RejectButton", "", "拒绝下分", 70, Color.LightCoral, Color.Black));
    }

    /// <summary>
    /// 配置当前期投注数据表格
    /// </summary>
    /// <param name="dataGridView">要配置的DataGridView</param>
    public static void ConfigureCurrentIssueBetDataGridView(DataGridView dataGridView)
    {
        ApplyBaseConfiguration(dataGridView);

        // 添加投注数据列（昵称列与账号列宽度统一）
        dataGridView.Columns.Add(CreateTextColumn("Issue", "期号", "Issue", 75));
        dataGridView.Columns.Add(CreateTextColumn("NickName", "昵称", "NickName", 80));
        dataGridView.Columns.Add(CreateTextColumn("Account", "账号", "Account", 80));
        dataGridView.Columns.Add(CreateTextColumn("PlayItem", "类型", "PlayItem", 55));
        dataGridView.Columns.Add(CreateTextColumn("Amount", "积分", "Amount", 60, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("FlightStatus", "飞单状态", "EnumFlightStatus", 75));

        // 设置列对齐方式
        dataGridView.Columns["Issue"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
        dataGridView.Columns["NickName"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
        dataGridView.Columns["Account"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
        dataGridView.Columns["PlayItem"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
        dataGridView.Columns["Amount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["FlightStatus"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

        // 设置排序
        dataGridView.Columns["Issue"].SortMode = DataGridViewColumnSortMode.Automatic;
        dataGridView.Columns["Amount"].SortMode = DataGridViewColumnSortMode.Automatic;
        dataGridView.Columns["FlightStatus"].SortMode = DataGridViewColumnSortMode.Automatic;

        // 设置最小列宽（昵称列与账号列宽度统一）
        dataGridView.Columns["Issue"].MinimumWidth = 65;
        dataGridView.Columns["NickName"].MinimumWidth = 70;
        dataGridView.Columns["Account"].MinimumWidth = 70;
        dataGridView.Columns["PlayItem"].MinimumWidth = 45;
        dataGridView.Columns["Amount"].MinimumWidth = 50;
        dataGridView.Columns["FlightStatus"].MinimumWidth = 65;

        // 设置固定列宽，确保紧凑布局（昵称列与账号列宽度统一）
        dataGridView.Columns["Issue"].Width = 75;
        dataGridView.Columns["NickName"].Width = 80;
        dataGridView.Columns["Account"].Width = 80;
        dataGridView.Columns["PlayItem"].Width = 55;
        dataGridView.Columns["Amount"].Width = 60;
        dataGridView.Columns["FlightStatus"].Width = 75;

        // 确保使用固定列宽模式（防止被其他地方重新设置）
        dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
    }

    /// <summary>
    /// 配置投注记录数据表格
    /// 显示11列：注单号、昵称、账号、期数、类型、金额、赔率、中奖、回水、结算、时间
    /// </summary>
    /// <param name="dataGridView">要配置的DataGridView</param>
    public static void ConfigureBetRecordDataGridView(DataGridView dataGridView)
    {
        // 确保在UI线程中执行
        if (dataGridView.InvokeRequired)
        {
            dataGridView.Invoke(() => ConfigureBetRecordDataGridView(dataGridView));
            return;
        }

        ApplyBaseConfiguration(dataGridView);

        // 添加投注记录数据列（11列，紧凑布局）
        dataGridView.Columns.Add(CreateTextColumn("OrderNumber", "注单号", "OrderNumber", 100));
        dataGridView.Columns.Add(CreateTextColumn("NickName", "昵称", "NickName", 70));
        dataGridView.Columns.Add(CreateTextColumn("Account", "账号", "Account", 75));
        dataGridView.Columns.Add(CreateTextColumn("Issue", "期数", "Issue", 75));
        dataGridView.Columns.Add(CreateTextColumn("PlayItem", "类型", "PlayItem", 60));
        dataGridView.Columns.Add(CreateTextColumn("Amount", "金额", "Amount", 70, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("Odds", "赔率", "Odds", 60, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("WinAmountText", "中奖", "WinAmountText", 80));
        dataGridView.Columns.Add(CreateTextColumn("RebateAmount", "回水", "RebateAmount", 60, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("SettlementAmountText", "结算", "SettlementAmountText", 80));
        dataGridView.Columns.Add(CreateTextColumn("CreatedTimeText", "时间", "CreatedTimeText", 130));

        // 设置列对齐方式
        dataGridView.Columns["OrderNumber"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
        dataGridView.Columns["NickName"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
        dataGridView.Columns["Account"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
        dataGridView.Columns["Issue"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
        dataGridView.Columns["PlayItem"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
        dataGridView.Columns["Amount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["Odds"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["WinAmountText"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["RebateAmount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["SettlementAmountText"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["CreatedTimeText"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

        // 设置排序
        dataGridView.Columns["OrderNumber"].SortMode = DataGridViewColumnSortMode.Automatic;
        dataGridView.Columns["Issue"].SortMode = DataGridViewColumnSortMode.Automatic;
        dataGridView.Columns["Amount"].SortMode = DataGridViewColumnSortMode.Automatic;
        dataGridView.Columns["CreatedTimeText"].SortMode = DataGridViewColumnSortMode.Automatic;

        // 设置最小列宽
        dataGridView.Columns["OrderNumber"].MinimumWidth = 90;
        dataGridView.Columns["NickName"].MinimumWidth = 60;
        dataGridView.Columns["Account"].MinimumWidth = 65;
        dataGridView.Columns["Issue"].MinimumWidth = 65;
        dataGridView.Columns["PlayItem"].MinimumWidth = 50;
        dataGridView.Columns["Amount"].MinimumWidth = 60;
        dataGridView.Columns["Odds"].MinimumWidth = 50;
        dataGridView.Columns["WinAmountText"].MinimumWidth = 70;
        dataGridView.Columns["RebateAmount"].MinimumWidth = 50;
        dataGridView.Columns["SettlementAmountText"].MinimumWidth = 70;
        dataGridView.Columns["CreatedTimeText"].MinimumWidth = 120;

        // 设置固定列宽，确保紧凑布局
        dataGridView.Columns["OrderNumber"].Width = 100;
        dataGridView.Columns["NickName"].Width = 70;
        dataGridView.Columns["Account"].Width = 75;
        dataGridView.Columns["Issue"].Width = 75;
        dataGridView.Columns["PlayItem"].Width = 60;
        dataGridView.Columns["Amount"].Width = 70;
        dataGridView.Columns["Odds"].Width = 60;
        dataGridView.Columns["WinAmountText"].Width = 80;
        dataGridView.Columns["RebateAmount"].Width = 60;
        dataGridView.Columns["SettlementAmountText"].Width = 80;
        dataGridView.Columns["CreatedTimeText"].Width = 130;

        // 确保使用固定列宽模式
        dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
    }

    /// <summary>
    /// 配置上下分记录数据表格
    /// 显示11列：单号、总上分、总下分、昵称、账号、类型、积分、创建时间、处理状态、处理时间、是否为假人
    /// </summary>
    /// <param name="dataGridView">要配置的DataGridView</param>
    public static void ConfigureDepositWithdrawRecordDataGridView(DataGridView dataGridView)
    {
        // 确保在UI线程中执行
        if (dataGridView.InvokeRequired)
        {
            dataGridView.Invoke(() => ConfigureDepositWithdrawRecordDataGridView(dataGridView));
            return;
        }

        ApplyBaseConfiguration(dataGridView);

        // 添加上下分记录数据列（11列，紧凑布局）
        dataGridView.Columns.Add(CreateTextColumn("OrderNumber", "单号", "OrderNumber", 100));
        dataGridView.Columns.Add(CreateTextColumn("TotalDeposit", "总上分", "TotalDeposit", 80, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("TotalWithdraw", "总下分", "TotalWithdraw", 80, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("NickName", "昵称", "NickName", 70));
        dataGridView.Columns.Add(CreateTextColumn("Account", "账号", "Account", 75));
        dataGridView.Columns.Add(CreateTextColumn("Type", "类型", "Type", 60));
        dataGridView.Columns.Add(CreateTextColumn("Amount", "积分", "Amount", 80, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("CreatedTimeText", "创建时间", "CreatedTimeText", 130));
        dataGridView.Columns.Add(CreateTextColumn("StatusText", "处理状态", "StatusText", 70));
        dataGridView.Columns.Add(CreateTextColumn("ProcessedTimeText", "处理时间", "ProcessedTimeText", 130));
        dataGridView.Columns.Add(CreateTextColumn("IsFakeUserText", "是否为假人", "IsFakeUserText", 80));

        // 设置列对齐方式
        dataGridView.Columns["OrderNumber"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
        dataGridView.Columns["TotalDeposit"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["TotalWithdraw"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["NickName"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
        dataGridView.Columns["Account"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
        dataGridView.Columns["Type"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
        dataGridView.Columns["Amount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["CreatedTimeText"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
        dataGridView.Columns["StatusText"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
        dataGridView.Columns["ProcessedTimeText"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
        dataGridView.Columns["IsFakeUserText"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

        // 设置排序
        dataGridView.Columns["OrderNumber"].SortMode = DataGridViewColumnSortMode.Automatic;
        dataGridView.Columns["TotalDeposit"].SortMode = DataGridViewColumnSortMode.Automatic;
        dataGridView.Columns["TotalWithdraw"].SortMode = DataGridViewColumnSortMode.Automatic;
        dataGridView.Columns["Amount"].SortMode = DataGridViewColumnSortMode.Automatic;
        dataGridView.Columns["CreatedTimeText"].SortMode = DataGridViewColumnSortMode.Automatic;

        // 设置最小列宽
        dataGridView.Columns["OrderNumber"].MinimumWidth = 90;
        dataGridView.Columns["TotalDeposit"].MinimumWidth = 70;
        dataGridView.Columns["TotalWithdraw"].MinimumWidth = 70;
        dataGridView.Columns["NickName"].MinimumWidth = 60;
        dataGridView.Columns["Account"].MinimumWidth = 65;
        dataGridView.Columns["Type"].MinimumWidth = 50;
        dataGridView.Columns["Amount"].MinimumWidth = 70;
        dataGridView.Columns["CreatedTimeText"].MinimumWidth = 120;
        dataGridView.Columns["StatusText"].MinimumWidth = 60;
        dataGridView.Columns["ProcessedTimeText"].MinimumWidth = 120;
        dataGridView.Columns["IsFakeUserText"].MinimumWidth = 70;

        // 设置固定列宽，确保紧凑布局
        dataGridView.Columns["OrderNumber"].Width = 100;
        dataGridView.Columns["TotalDeposit"].Width = 80;
        dataGridView.Columns["TotalWithdraw"].Width = 80;
        dataGridView.Columns["NickName"].Width = 70;
        dataGridView.Columns["Account"].Width = 75;
        dataGridView.Columns["Type"].Width = 60;
        dataGridView.Columns["Amount"].Width = 80;
        dataGridView.Columns["CreatedTimeText"].Width = 130;
        dataGridView.Columns["StatusText"].Width = 70;
        dataGridView.Columns["ProcessedTimeText"].Width = 130;
        dataGridView.Columns["IsFakeUserText"].Width = 80;

        // 确保使用固定列宽模式
        dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
    }

    /// <summary>
    /// 配置输赢流水回水记录数据表格
    /// 显示12列：昵称、账号、真盈亏、假盈亏、总盈亏、真流水、假流水、总流水、未回水、回水比例、已回水、总回水
    /// </summary>
    /// <param name="dataGridView">要配置的DataGridView</param>
    public static void ConfigureWinLossRebateRecordDataGridView(DataGridView dataGridView)
    {
        // 确保在UI线程中执行
        if (dataGridView.InvokeRequired)
        {
            dataGridView.Invoke(() => ConfigureWinLossRebateRecordDataGridView(dataGridView));
            return;
        }

        ApplyBaseConfiguration(dataGridView);

        // 添加输赢流水回水记录数据列（12列，紧凑布局）
        dataGridView.Columns.Add(CreateTextColumn("NickName", "昵称", "NickName", 70));
        dataGridView.Columns.Add(CreateTextColumn("Account", "账号", "Account", 75));
        dataGridView.Columns.Add(CreateTextColumn("RealProfitLoss", "真盈亏", "RealProfitLoss", 80, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("FakeProfitLoss", "假盈亏", "FakeProfitLoss", 80, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("TotalProfitLoss", "总盈亏", "TotalProfitLoss", 80, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("RealTurnover", "真流水", "RealTurnover", 80, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("FakeTurnover", "假流水", "FakeTurnover", 80, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("TotalTurnover", "总流水", "TotalTurnover", 80, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("PendingRebate", "未回水", "PendingRebate", 80, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("RebatePercent", "回水比例", "RebatePercent", 80, "F1"));
        dataGridView.Columns.Add(CreateTextColumn("PaidRebate", "已回水", "PaidRebate", 80, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("TotalRebate", "总回水", "TotalRebate", 80, "F2"));

        // 设置列对齐方式
        dataGridView.Columns["NickName"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
        dataGridView.Columns["Account"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
        dataGridView.Columns["RealProfitLoss"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["FakeProfitLoss"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["TotalProfitLoss"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["RealTurnover"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["FakeTurnover"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["TotalTurnover"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["PendingRebate"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["RebatePercent"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["PaidRebate"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        dataGridView.Columns["TotalRebate"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

        // 设置排序
        dataGridView.Columns["Account"].SortMode = DataGridViewColumnSortMode.Automatic;
        dataGridView.Columns["TotalProfitLoss"].SortMode = DataGridViewColumnSortMode.Automatic;
        dataGridView.Columns["TotalTurnover"].SortMode = DataGridViewColumnSortMode.Automatic;
        dataGridView.Columns["TotalRebate"].SortMode = DataGridViewColumnSortMode.Automatic;
        dataGridView.Columns["PendingRebate"].SortMode = DataGridViewColumnSortMode.Automatic;

        // 设置最小列宽
        dataGridView.Columns["NickName"].MinimumWidth = 60;
        dataGridView.Columns["Account"].MinimumWidth = 65;
        dataGridView.Columns["RealProfitLoss"].MinimumWidth = 70;
        dataGridView.Columns["FakeProfitLoss"].MinimumWidth = 70;
        dataGridView.Columns["TotalProfitLoss"].MinimumWidth = 70;
        dataGridView.Columns["RealTurnover"].MinimumWidth = 70;
        dataGridView.Columns["FakeTurnover"].MinimumWidth = 70;
        dataGridView.Columns["TotalTurnover"].MinimumWidth = 70;
        dataGridView.Columns["PendingRebate"].MinimumWidth = 70;
        dataGridView.Columns["RebatePercent"].MinimumWidth = 70;
        dataGridView.Columns["PaidRebate"].MinimumWidth = 70;
        dataGridView.Columns["TotalRebate"].MinimumWidth = 70;

        // 设置固定列宽，确保紧凑布局
        dataGridView.Columns["NickName"].Width = 70;
        dataGridView.Columns["Account"].Width = 75;
        dataGridView.Columns["RealProfitLoss"].Width = 80;
        dataGridView.Columns["FakeProfitLoss"].Width = 80;
        dataGridView.Columns["TotalProfitLoss"].Width = 80;
        dataGridView.Columns["RealTurnover"].Width = 80;
        dataGridView.Columns["FakeTurnover"].Width = 80;
        dataGridView.Columns["TotalTurnover"].Width = 80;
        dataGridView.Columns["PendingRebate"].Width = 80;
        dataGridView.Columns["RebatePercent"].Width = 80;
        dataGridView.Columns["PaidRebate"].Width = 80;
        dataGridView.Columns["TotalRebate"].Width = 80;

        // 确保使用固定列宽模式
        dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
    }

    /// <summary>
    /// 配置拉手返点记录数据表格
    /// 显示6列：昵称、账号、当天流水、拉手返点、返水、操作
    /// </summary>
    /// <param name="dataGridView">要配置的DataGridView</param>
    public static void ConfigureAgentRebateRecordDataGridView(DataGridView dataGridView)
    {
        // 确保在UI线程中执行
        if (dataGridView.InvokeRequired)
        {
            dataGridView.Invoke(() => ConfigureAgentRebateRecordDataGridView(dataGridView));
            return;
        }

        ApplyBaseConfiguration(dataGridView);

        // 添加列
        dataGridView.Columns.Add(CreateTextColumn("NickName", @"昵称", "NickName", 120));
        dataGridView.Columns.Add(CreateTextColumn("Account", @"账号", "Account", 120));
        dataGridView.Columns.Add(CreateTextColumn("ValidTurnover", @"当天流水", "ValidTurnover", 100, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("AgentRebatePercent", @"拉手返点", "AgentRebatePercent", 80, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("RebateAmount", @"返水", "RebateAmount", 100, "F2"));
        dataGridView.Columns.Add(CreateTextColumn("Operation", @"操作", "Operation", 80));

        // 设置列宽
        dataGridView.Columns["NickName"].Width = 120;
        dataGridView.Columns["Account"].Width = 120;
        dataGridView.Columns["ValidTurnover"].Width = 100;
        dataGridView.Columns["AgentRebatePercent"].Width = 80;
        dataGridView.Columns["RebateAmount"].Width = 100;
        dataGridView.Columns["Operation"].Width = 80;

        // 确保使用固定列宽模式
        dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
    }

    #endregion
}
