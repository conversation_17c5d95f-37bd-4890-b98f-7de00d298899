using System.ComponentModel.DataAnnotations;
using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

/// <summary>
/// 赔率配置数据库模型
/// 用于存储投注项目的赔率配置信息
/// </summary>
[Table(Name = @"OddsConfigs")]
[Index(@"idx_play_item", nameof(PlayItem), true)] // 投注项目唯一索引
public class OddsConfig
{
    /// <summary>
    /// 主键ID，自增长
    /// </summary>
    [Column(IsIdentity = true, IsPrimary = true)]
    public int Id { get; set; }

    /// <summary>
    /// 投注项目名称
    /// 如：1正、2正、1番、2番、12角、1念2、单、双等
    /// </summary>
    [Required(ErrorMessage = @"投注项目名称不能为空")]
    [StringLength(20, ErrorMessage = @"投注项目名称长度不能超过20个字符")]
    [Column(StringLength = 20)]
    public string PlayItem { get; set; } = string.Empty;

    /// <summary>
    /// 赔率
    /// </summary>
    [Range(1.0, 100.0, ErrorMessage = @"赔率必须在1.0-100.0之间")]
    [Column(Precision = 8, Scale = 3)]
    public decimal Odds { get; set; }

    /// <summary>
    /// 最小投注金额
    /// </summary>
    [Range(0.01, double.MaxValue, ErrorMessage = @"最小投注金额必须大于0")]
    [Column(Precision = 18, Scale = 2)]
    public decimal MinStake { get; set; }

    /// <summary>
    /// 最大投注金额
    /// </summary>
    [Range(0.01, double.MaxValue, ErrorMessage = @"最大投注金额必须大于0")]
    [Column(Precision = 18, Scale = 2)]
    public decimal MaxStake { get; set; }

    /// <summary>
    /// 总投注限额
    /// </summary>
    [Range(0.01, double.MaxValue, ErrorMessage = @"总投注限额必须大于0")]
    [Column(Precision = 18, Scale = 2)]
    public decimal TotalStake { get; set; }

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int SortOrder { get; set; } = 0;
}


