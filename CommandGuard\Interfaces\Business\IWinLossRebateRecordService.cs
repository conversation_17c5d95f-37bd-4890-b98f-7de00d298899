using CommandGuard.ViewModels;

namespace CommandGuard.Interfaces.Business;

/// <summary>
/// 输赢流水回水记录服务接口
/// 提供输赢流水回水记录的查询和统计功能
/// </summary>
public interface IWinLossRebateRecordService
{
    /// <summary>
    /// 根据条件查询输赢流水回水记录
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="account">账号（可选）</param>
    /// <param name="issue">期号（可选）</param>
    /// <param name="includeFakeUsers">是否包含假人</param>
    /// <returns>输赢流水回水记录列表</returns>
    Task<List<WinLossRebateRecordViewModel>> QueryWinLossRebateRecordsAsync(
        DateTime startTime,
        DateTime endTime,
        string? account = null,
        string? issue = null,
        bool includeFakeUsers = false);

    /// <summary>
    /// 计算总真盈亏
    /// </summary>
    /// <param name="records">记录列表</param>
    /// <returns>总真盈亏</returns>
    decimal CalculateTotalRealProfitLoss(List<WinLossRebateRecordViewModel> records);

    /// <summary>
    /// 计算总假盈亏
    /// </summary>
    /// <param name="records">记录列表</param>
    /// <returns>总假盈亏</returns>
    decimal CalculateTotalFakeProfitLoss(List<WinLossRebateRecordViewModel> records);

    /// <summary>
    /// 计算总盈亏
    /// </summary>
    /// <param name="records">记录列表</param>
    /// <returns>总盈亏</returns>
    decimal CalculateTotalProfitLoss(List<WinLossRebateRecordViewModel> records);

    /// <summary>
    /// 计算总真流水
    /// </summary>
    /// <param name="records">记录列表</param>
    /// <returns>总真流水</returns>
    decimal CalculateTotalRealTurnover(List<WinLossRebateRecordViewModel> records);

    /// <summary>
    /// 计算总假流水
    /// </summary>
    /// <param name="records">记录列表</param>
    /// <returns>总假流水</returns>
    decimal CalculateTotalFakeTurnover(List<WinLossRebateRecordViewModel> records);

    /// <summary>
    /// 计算总流水
    /// </summary>
    /// <param name="records">记录列表</param>
    /// <returns>总流水</returns>
    decimal CalculateTotalTurnover(List<WinLossRebateRecordViewModel> records);

    /// <summary>
    /// 计算总未回水
    /// </summary>
    /// <param name="records">记录列表</param>
    /// <returns>总未回水</returns>
    decimal CalculateTotalPendingRebate(List<WinLossRebateRecordViewModel> records);

    /// <summary>
    /// 计算总已回水
    /// </summary>
    /// <param name="records">记录列表</param>
    /// <returns>总已回水</returns>
    decimal CalculateTotalPaidRebate(List<WinLossRebateRecordViewModel> records);

    /// <summary>
    /// 计算总回水
    /// </summary>
    /// <param name="records">记录列表</param>
    /// <returns>总回水</returns>
    decimal CalculateTotalRebate(List<WinLossRebateRecordViewModel> records);

    /// <summary>
    /// 获取输赢流水回水记录统计信息
    /// </summary>
    /// <param name="records">记录列表</param>
    /// <returns>统计信息</returns>
    WinLossRebateStatistics GetWinLossRebateStatistics(List<WinLossRebateRecordViewModel> records);
}

/// <summary>
/// 输赢流水回水统计信息
/// </summary>
public class WinLossRebateStatistics
{
    /// <summary>
    /// 总真盈亏
    /// </summary>
    public decimal TotalRealProfitLoss { get; set; }

    /// <summary>
    /// 总假盈亏
    /// </summary>
    public decimal TotalFakeProfitLoss { get; set; }

    /// <summary>
    /// 总盈亏
    /// </summary>
    public decimal TotalProfitLoss { get; set; }

    /// <summary>
    /// 总真流水
    /// </summary>
    public decimal TotalRealTurnover { get; set; }

    /// <summary>
    /// 总假流水
    /// </summary>
    public decimal TotalFakeTurnover { get; set; }

    /// <summary>
    /// 总流水
    /// </summary>
    public decimal TotalTurnover { get; set; }

    /// <summary>
    /// 总未回水
    /// </summary>
    public decimal TotalPendingRebate { get; set; }

    /// <summary>
    /// 总已回水
    /// </summary>
    public decimal TotalPaidRebate { get; set; }

    /// <summary>
    /// 总回水
    /// </summary>
    public decimal TotalRebate { get; set; }

    /// <summary>
    /// 记录数量
    /// </summary>
    public int RecordCount { get; set; }

    /// <summary>
    /// 平均回水比例
    /// </summary>
    public decimal AverageRebatePercent { get; set; }

    /// <summary>
    /// 回水完成率
    /// </summary>
    public decimal RebateCompletionRate { get; set; }
}
