using CommandGuard.ViewModels;

namespace CommandGuard.Interfaces.Business;

/// <summary>
/// 拉手返点记录服务接口
/// 提供拉手返点记录的查询、统计等功能
/// </summary>
public interface IAgentRebateRecordService
{
    /// <summary>
    /// 根据条件查询拉手返点记录
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="agentName">拉手名称（可选）</param>
    /// <returns>拉手返点记录列表</returns>
    Task<List<AgentRebateRecordViewModel>> QueryAgentRebateRecordsAsync(
        DateTime startTime,
        DateTime endTime,
        string? agentName = null);

    /// <summary>
    /// 计算总有效流水
    /// </summary>
    /// <param name="records">拉手返点记录列表</param>
    /// <returns>总有效流水</returns>
    decimal CalculateTotalValidTurnover(List<AgentRebateRecordViewModel> records);

    /// <summary>
    /// 计算总返水金额
    /// </summary>
    /// <param name="records">拉手返点记录列表</param>
    /// <returns>总返水金额</returns>
    decimal CalculateTotalRebateAmount(List<AgentRebateRecordViewModel> records);

    /// <summary>
    /// 获取活跃会员数量
    /// </summary>
    /// <param name="records">拉手返点记录列表</param>
    /// <returns>活跃会员数量（有流水的会员）</returns>
    int GetActiveAgentCount(List<AgentRebateRecordViewModel> records);

    /// <summary>
    /// 获取会员总数
    /// </summary>
    /// <param name="records">拉手返点记录列表</param>
    /// <returns>会员总数</returns>
    int GetTotalAgentCount(List<AgentRebateRecordViewModel> records);

    /// <summary>
    /// 获取拉手返点记录统计信息
    /// </summary>
    /// <param name="records">拉手返点记录列表</param>
    /// <returns>统计信息元组（总流水，总返水，活跃会员数，会员总数）</returns>
    (decimal TotalValidTurnover, decimal TotalRebateAmount, int ActiveAgentCount, int TotalAgentCount)
        GetAgentRebateStatistics(List<AgentRebateRecordViewModel> records);
}
