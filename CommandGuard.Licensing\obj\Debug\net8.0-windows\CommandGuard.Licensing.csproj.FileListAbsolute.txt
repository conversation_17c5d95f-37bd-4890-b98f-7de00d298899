F:\SolutionCommandGuard\CommandGuard.Licensing\bin\Debug\net8.0-windows\CommandGuard.Licensing.deps.json
F:\SolutionCommandGuard\CommandGuard.Licensing\bin\Debug\net8.0-windows\CommandGuard.Licensing.dll
F:\SolutionCommandGuard\CommandGuard.Licensing\bin\Debug\net8.0-windows\CommandGuard.Licensing.pdb
F:\SolutionCommandGuard\CommandGuard.Licensing\obj\Debug\net8.0-windows\CommandGuard.Licensing.csproj.AssemblyReference.cache
F:\SolutionCommandGuard\CommandGuard.Licensing\obj\Debug\net8.0-windows\CommandGuard.Licensing.GeneratedMSBuildEditorConfig.editorconfig
F:\SolutionCommandGuard\CommandGuard.Licensing\obj\Debug\net8.0-windows\CommandGuard.Licensing.AssemblyInfoInputs.cache
F:\SolutionCommandGuard\CommandGuard.Licensing\obj\Debug\net8.0-windows\CommandGuard.Licensing.AssemblyInfo.cs
F:\SolutionCommandGuard\CommandGuard.Licensing\obj\Debug\net8.0-windows\CommandGuard.Licensing.csproj.CoreCompileInputs.cache
F:\SolutionCommandGuard\CommandGuard.Licensing\obj\Debug\net8.0-windows\CommandGuard.Licensing.dll
F:\SolutionCommandGuard\CommandGuard.Licensing\obj\Debug\net8.0-windows\refint\CommandGuard.Licensing.dll
F:\SolutionCommandGuard\CommandGuard.Licensing\obj\Debug\net8.0-windows\CommandGuard.Licensing.pdb
F:\SolutionCommandGuard\CommandGuard.Licensing\obj\Debug\net8.0-windows\ref\CommandGuard.Licensing.dll
F:\RobotCopy\SolutionCommandGuard\CommandGuard.Licensing\bin\Debug\net8.0-windows\CommandGuard.Licensing.deps.json
F:\RobotCopy\SolutionCommandGuard\CommandGuard.Licensing\bin\Debug\net8.0-windows\CommandGuard.Licensing.dll
F:\RobotCopy\SolutionCommandGuard\CommandGuard.Licensing\bin\Debug\net8.0-windows\CommandGuard.Licensing.pdb
F:\RobotCopy\SolutionCommandGuard\CommandGuard.Licensing\obj\Debug\net8.0-windows\CommandGuard.Licensing.csproj.AssemblyReference.cache
F:\RobotCopy\SolutionCommandGuard\CommandGuard.Licensing\obj\Debug\net8.0-windows\CommandGuard.Licensing.GeneratedMSBuildEditorConfig.editorconfig
F:\RobotCopy\SolutionCommandGuard\CommandGuard.Licensing\obj\Debug\net8.0-windows\CommandGuard.Licensing.AssemblyInfoInputs.cache
F:\RobotCopy\SolutionCommandGuard\CommandGuard.Licensing\obj\Debug\net8.0-windows\CommandGuard.Licensing.AssemblyInfo.cs
F:\RobotCopy\SolutionCommandGuard\CommandGuard.Licensing\obj\Debug\net8.0-windows\CommandGuard.Licensing.csproj.CoreCompileInputs.cache
F:\RobotCopy\SolutionCommandGuard\CommandGuard.Licensing\obj\Debug\net8.0-windows\CommandGuard.Licensing.dll
F:\RobotCopy\SolutionCommandGuard\CommandGuard.Licensing\obj\Debug\net8.0-windows\refint\CommandGuard.Licensing.dll
F:\RobotCopy\SolutionCommandGuard\CommandGuard.Licensing\obj\Debug\net8.0-windows\CommandGuard.Licensing.pdb
F:\RobotCopy\SolutionCommandGuard\CommandGuard.Licensing\obj\Debug\net8.0-windows\ref\CommandGuard.Licensing.dll
