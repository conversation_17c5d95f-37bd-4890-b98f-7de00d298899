﻿namespace CommandGuard.Interfaces.Chat;

public interface IChatService
{
    #region 核心方法

    Task GetRobotInfoAsync();
    Task GetGroupDicAsync();
    Task<string> GetNickNameAsync(string account);
    Task SendGroupMessageAsync(string message);
    Task SendGroupMessageAsync(string message, string atAccount);
    Task SendImageAsync(string imgPath);

    #endregion

    #region 公共方法

    Task SendOpenTipsAsync();
    Task Send30SecondCloseTipsAsync(int closeTimeSpan);
    Task SendStopTipsAsync();
    Task SendAvoidControversyTipsAsync();
    Task SendAntiFraudTipsAsync();

    #endregion
}