using System.ComponentModel.DataAnnotations;
using CommandGuard.Enums;
using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

/// <summary>
/// 下分申请模型
/// 用户申请减少余额的订单记录
/// </summary>
[Table(Name = @"WithdrawRequests")]
[Index(@"idx_withdraw_account", nameof(Account))]
[Index(@"idx_withdraw_status", nameof(Status))]
[Index(@"idx_withdraw_created", nameof(CreatedTime))]
public class WithdrawRequest
{
    /// <summary>
    /// 申请ID，自增主键
    /// </summary>
    [Column(IsIdentity = true, IsPrimary = true)]
    public long Id { get; set; }

    /// <summary>
    /// 申请用户账号
    /// </summary>
    [Required(ErrorMessage = @"用户账号不能为空")]
    [StringLength(50, ErrorMessage = @"用户账号长度不能超过50个字符")]
    [Column(StringLength = 50)]
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 申请金额
    /// </summary>
    [Range(0.01, double.MaxValue, ErrorMessage = @"申请金额必须大于0")]
    [Column(Precision = 18, Scale = 2)]
    public decimal Amount { get; set; }

    /// <summary>
    /// 申请状态
    /// </summary>
    public EnumWithdrawStatus Status { get; set; } = EnumWithdrawStatus.Pending;

    // 移除OriginalMessage字段，简化WithdrawRequest模型
    // /// <summary>
    // /// 原始消息内容
    // /// </summary>
    // [StringLength(500, ErrorMessage = @"原始消息内容长度不能超过500个字符")]
    // [Column(StringLength = 500)]
    // public string OriginalMessage { get; set; } = string.Empty;

    /// <summary>
    /// 关联的消息ID
    /// </summary>
    public string MessageId { get; set; }=string.Empty;

    /// <summary>
    /// 申请前余额
    /// </summary>
    [Column(Precision = 18, Scale = 2)]
    public decimal BalanceBeforeRequest { get; set; }

    /// <summary>
    /// 是否已冻结金额
    /// </summary>
    public bool IsFrozen { get; set; } = false;

    /// <summary>
    /// 创建时间
    /// </summary>
    [Column(ServerTime = DateTimeKind.Local)]
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 审核时间
    /// </summary>
    public DateTime? ReviewTime { get; set; }
}
