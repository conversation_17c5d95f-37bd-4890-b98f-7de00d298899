﻿using CommandGuard.Enums;
using CommandGuard.Interfaces.Lottery;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Lottery;

/// <summary>
/// 台湾宾果3配置服务实现
/// </summary>
#pragma warning disable CS9113 // 参数未读
public class LotteryConfigurationService(ILogger<LotteryConfigurationService> logger) : ILotteryConfigurationService
#pragma warning restore CS9113
{
    /// <summary>
    /// 当前彩票游戏类型（固定为台湾宾果）
    /// </summary>
    public EnumLottery CurrentLottery => EnumLottery.台湾宾果;

    /// <summary>
    /// 获取空开奖号码字符串
    /// </summary>
    public string GetEmptyDrawNumbers()
    {
        // 台湾宾果：21个0
        return "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0";
    }

    /// <summary>
    /// 获取最大数据条数
    /// </summary>
    public int GetMaxDataCount()
    {
        // 台湾宾果固定返回20
        return 20;
    }

    /// <summary>
    /// 获取数据查询标识
    /// </summary>
    public string GetDataQueryFlag(Models.KaiJiang kaiJiang)
    {
        // 台湾宾果：使用时间前10位（日期）
        return kaiJiang.Time.Substring(0, 10);
    }

    /// <summary>
    /// 判断是否使用时间查询
    /// </summary>
    public bool UseTimeQuery()
    {
        // 台湾宾果固定使用时间查询
        return true;
    }
}