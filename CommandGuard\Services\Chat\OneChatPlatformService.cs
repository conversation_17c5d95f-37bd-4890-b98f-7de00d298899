using CommandGuard.Configuration;
using CommandGuard.Interfaces.Business;
using CommandGuard.Interfaces.Chat;
using Flurl.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;

namespace CommandGuard.Services.Chat;

/// <summary>
/// 一起聊吧平台服务实现 - 一起聊吧聊天平台的具体实现
/// 提供一起聊吧平台的机器人操作、消息发送、用户信息获取等功能
/// 基于HTTP API与一起聊吧客户端进行通信
/// </summary>
public class OneChatPlatformService(
    ILogger<OneChatPlatformService> logger,
    IMemberService memberService) : IChatPlatformHelper
{
    /// <summary>
    /// OneChat服务器地址
    /// </summary>
    private static string Host => @"http://127.0.0.1:3001";

    /// <summary>
    /// 机器人用户ID
    /// </summary>
    private static string Uid => @"admin";

    /// <summary>
    /// 获取平台名称
    /// </summary>
    public string GetPlatformName() => "一起聊吧";

    /// <summary>
    /// 获取机器人信息
    /// </summary>
    public async Task GetRobotInfoAsync()
    {
        RuntimeConfiguration.UpdateRobotAccount("admin");
        RuntimeConfiguration.UpdateRobotNickName("系统管理员");
        await Task.Delay(0);

        logger.LogInformation(@"一起聊吧机器人信息获取成功，账号: {Account}, 昵称: {NickName}",
            RuntimeConfiguration.GetRobotAccount(), RuntimeConfiguration.GetRobotNickName());
    }

    /// <summary>
    /// 获取群组字典信息
    /// </summary>
    public async Task GetGroupDicAsync()
    {
        RuntimeConfiguration.AddOrUpdateGroup("OneChat", "一起聊吧");
        await Task.Delay(0);

        logger.LogInformation(@"一起聊吧群组信息获取成功，群号: OneChat, 群名: 一起聊吧");
    }

    /// <summary>
    /// 获取用户昵称 - 与OneChatHelper.cs完全一致
    /// </summary>
    /// <param name="account">用户ID</param>
    /// <returns>用户昵称，如果获取失败则返回空字符串</returns>
    public async Task<string> GetNickNameAsync(string account)
    {
        try
        {
            // 与OneChatHelper.cs完全一致的实现
            string url = $"{Host}/api/users/getNickName?uid={account}";
            string response = await url
                .WithTimeout(TimeSpan.FromSeconds(3))
                .GetStringAsync();

            if (!string.IsNullOrWhiteSpace(response) && response.Contains("nickname"))
            {
                JObject jObject = JObject.Parse(response);
                string nickName = jObject["nickname"]!.ToString();

                logger.LogDebug(@"一起聊吧获取昵称成功，账号: {Account}, 昵称: {NickName}", account, nickName);
                return nickName;
            }
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, @"一起聊吧获取昵称失败，账号: {Account}", account);
        }

        return "";
    }

    /// <summary>
    /// 发送群组消息
    /// </summary>
    /// <param name="message">消息内容</param>
    public async Task SendGroupMessageAsync(string message)
    {
        try
        {
            // 与OneChatHelper.cs完全一致的实现
            message = message.Replace("\r", Environment.NewLine);

            // 发送请求
            await $"{Host}/api/messages/sendText"
                .PostJsonAsync(new
                {
                    Uid,
                    Content = message
                });

            logger.LogInformation(@"一起聊吧群组消息发送成功: {Message}", message);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"一起聊吧发送群组消息失败: {Message}", message);
        }
    }

    /// <summary>
    /// 发送群组消息并@指定用户
    /// </summary>
    /// <param name="message">消息内容</param>
    /// <param name="atAccount">要@的用户账号</param>
    public async Task SendGroupMessageAsync(string message, string atAccount)
    {
        try
        {
            // 与OneChatHelper.cs完全一致的实现
            message = message.Replace("\r", Environment.NewLine);
            //string nickName = atAccount; // 暂时使用账号作为昵称，后续可通过服务获取真实昵称
            string nickName = (await memberService.GetMemberAsync(atAccount))!.NickName;

            // 发送请求
            await $"{Host}/api/messages/sendText"
                .PostJsonAsync(new
                {
                    Uid,
                    content = "@" + nickName + message
                });

            logger.LogInformation(@"一起聊吧群组@消息发送成功: {Message}, @用户: {AtAccount}", message, atAccount);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"一起聊吧发送群组@消息失败: {Message}, @用户: {AtAccount}", message, atAccount);
        }
    }

    /// <summary>
    /// 发送图片消息
    /// </summary>
    /// <param name="imgPath">图片路径</param>
    public async Task SendImageAsync(string imgPath)
    {
        try
        {
            // 与OneChatHelper.cs完全一致的实现
            // 验证文件存在
            if (!File.Exists(imgPath))
            {
                logger.LogWarning(@"文件不存在: {ImagePath}", imgPath);
                return;
            }

            // 验证文件类型
            string extension = Path.GetExtension(imgPath).ToLower();
            if (extension != ".jpg" && extension != ".jpeg" && extension != ".png" && extension != ".gif")
            {
                logger.LogWarning(@"不支持的图片格式，仅支持JPG、PNG和GIF: {ImagePath}", imgPath);
                return;
            }

            // 构建API URL
            string apiUrl = $"{Host}/api/messages/sendImage";

            // 上传图片
            await apiUrl
                .PostMultipartAsync(mp =>
                {
                    mp.AddString("Uid", Uid);
                    mp.AddFile("image", imgPath);
                });

            logger.LogInformation(@"一起聊吧群组图片发送成功: {ImagePath}", imgPath);
        }
        catch (FlurlHttpException ex)
        {
            logger.LogError(ex, @"一起聊吧发送群组图片HTTP请求失败: {ImagePath}", imgPath);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"一起聊吧发送群组图片失败: {ImagePath}", imgPath);
        }
    }
}