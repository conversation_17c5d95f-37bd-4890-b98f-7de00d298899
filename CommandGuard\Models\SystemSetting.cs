using System.ComponentModel.DataAnnotations;
using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

/// <summary>
/// 系统设置数据库模型
/// 用于存储系统的各种配置参数
/// </summary>
[Table(Name = @"SystemSettings")]
[Index(@"idx_setting_key", nameof(SettingKey), true)] // 设置键唯一索引
public class SystemSetting
{
    /// <summary>
    /// 主键ID，自增长
    /// </summary>
    [Column(IsIdentity = true, IsPrimary = true)]
    public int Id { get; set; }

    /// <summary>
    /// 设置键名
    /// 如：是否发送开奖图、回水比例、开盘时间等
    /// </summary>
    [Required(ErrorMessage = @"设置键名不能为空")]
    [StringLength(100, ErrorMessage = @"设置键名长度不能超过100个字符")]
    [Column(StringLength = 100)]
    public string SettingKey { get; set; } = string.Empty;

    /// <summary>
    /// 设置值
    /// 存储为字符串，根据SettingType进行类型转换
    /// </summary>
    [Required(ErrorMessage = @"设置值不能为空")]
    [StringLength(500, ErrorMessage = @"设置值长度不能超过500个字符")]
    [Column(StringLength = 500)]
    public string SettingValue { get; set; } = string.Empty;

    /// <summary>
    /// 设置类型
    /// Boolean、Integer、Decimal、String
    /// </summary>
    [Required(ErrorMessage = @"设置类型不能为空")]
    [StringLength(50, ErrorMessage = @"设置类型长度不能超过50个字符")]
    [Column(StringLength = 50)]
    public string SettingType { get; set; } = string.Empty;

    /// <summary>
    /// 默认值
    /// 用于重置功能
    /// </summary>
    [StringLength(500, ErrorMessage = @"默认值长度不能超过500个字符")]
    [Column(StringLength = 500)]
    public string DefaultValue { get; set; } = string.Empty;

    /// <summary>
    /// 排序顺序
    /// 用于控制设置项在界面中的显示顺序，数值越小越靠前
    /// </summary>
    [Range(0, int.MaxValue, ErrorMessage = @"排序顺序必须为非负整数")]
    public int SortOrder { get; set; } = 0;
}

/// <summary>
/// 系统设置类型常量
/// </summary>
public static class SystemSettingTypes
{
    /// <summary>
    /// 布尔类型
    /// </summary>
    public const string Boolean = @"Boolean";

    /// <summary>
    /// 整数类型
    /// </summary>
    public const string Integer = @"Integer";

    /// <summary>
    /// 小数类型
    /// </summary>
    public const string Decimal = @"Decimal";

    /// <summary>
    /// 字符串类型
    /// </summary>
    public const string String = @"String";
}