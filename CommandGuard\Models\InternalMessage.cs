using System.ComponentModel.DataAnnotations;
using CommandGuard.Enums;
using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

/// <summary>
/// 内部消息模型
/// 用于存储转换后的统一格式消息到数据库
/// 包含完整的消息信息和处理状态
/// </summary>
[Table(Name = "Messages")]
[Index("idx_message_status", nameof(Status))]
[Index("idx_message_created", nameof(CreatedTime))]
public class InternalMessage
{
    /// <summary>
    /// 消息主键ID
    /// 数据库自增主键
    /// </summary>
    [Column(IsPrimary = true)]
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 消息创建时间
    /// 消息在本系统中的创建时间
    /// </summary>
    [Column(ServerTime = DateTimeKind.Local)]
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 用户账号
    /// 来自外部消息的用户账号信息
    /// </summary>
    [Required(ErrorMessage = @"用户账号不能为空")]
    [Column(StringLength = -1)] // 无限长度，对应TEXT类型
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 用户昵称
    /// 来自外部消息的用户昵称信息
    /// </summary>
    [Column(StringLength = -1)] // 无限长度，对应TEXT类型
    public string NickName { get; set; } = string.Empty;

    /// <summary>
    /// 消息内容
    /// 消息的详细内容
    /// </summary>
    [Required(ErrorMessage = @"消息内容不能为空")]
    [Column(StringLength = -1)] // 无限长度，对应TEXT类型
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 消息处理状态
    /// 0: 待处理, 1: 处理中, 2: 已处理, 3: 处理失败
    /// </summary>
    public EnumMessageStatus Status { get; set; } = EnumMessageStatus.Pending;

    /// <summary>
    /// 消息更新时间
    /// 消息最后更新时间
    /// </summary>
    public DateTime? UpdatedTime { get; set; }

    /// <summary>
    /// 外部消息ID
    /// 来源平台的原始消息ID
    /// </summary>
    [StringLength(100, ErrorMessage = @"外部消息ID长度不能超过100个字符")]
    [Column(StringLength = 100)]
    public string ExternalMessageId { get; set; } = string.Empty;

    /// <summary>
    /// 外部平台名称
    /// 消息来源的聊天平台标识（如：OneChat、MyQQ等）
    /// </summary>
    [StringLength(50, ErrorMessage = @"外部平台名称长度不能超过50个字符")]
    [Column(StringLength = 50)]
    public string ExternalPlatform { get; set; } = string.Empty;

    /// <summary>
    /// 外部消息创建时间
    /// 消息在原始平台的创建时间
    /// </summary>
    public DateTime? ExternalCreatedTime { get; set; }
}