﻿<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=D_003A_005CTreeDLL_005CAiHelper_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AConsolePal_002EWindows_002Ecs_002Fl_003AC_0021_003FUsers_003Fphusun_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FSourcesCache_003F0f04514edab49ce421fdf1e12235d4b26bfd8cc473ef7f135bb285d607a9e8_003FConsolePal_002EWindows_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AISelect0_002Ecs_002Fl_003AC_0021_003FUsers_003Fphusun_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FSourcesCache_003Faa36dbd3866a97a4fa3ffc4c63abc844365187604a18134ca52da99bdc2d90_003FISelect0_002Ecs/@EntryIndexedValue">ForceIncluded</s:String></wpf:ResourceDictionary>