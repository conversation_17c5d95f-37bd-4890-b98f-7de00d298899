﻿using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

/// <summary>
/// 投注历史记录实体类
/// 用于存储用户的投注记录信息
/// </summary>
[Table(Name = "BetHistory")]
public class BetHistory
{
    /// <summary>
    /// 主键ID，自增长
    /// </summary>
    [Column(IsIdentity = true, IsPrimary = true)]
    public long Id { get; set; }

    /// <summary>
    /// 投注单号
    /// 平台生成的唯一投注编号
    /// </summary>
    public string BetNo { get; set; } = string.Empty;

    /// <summary>
    /// 投注时间
    /// 格式为：yyyy-MM-dd HH:mm:ss
    /// </summary>
    public string Time { get; set; } = string.Empty;

    /// <summary>
    /// 游戏名称
    /// 例如：台湾宾果1、168飞艇前3等
    /// </summary>
    public string GameName { get; set; } = string.Empty;

    /// <summary>
    /// 投注期号
    /// 对应游戏的特定期号
    /// </summary>
    public string Issue { get; set; } = string.Empty;

    /// <summary>
    /// 投注内容
    /// 包含具体的投注项，如：单、双、1番等
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 投注金额
    /// </summary>
    public string Money { get; set; } = string.Empty;
}