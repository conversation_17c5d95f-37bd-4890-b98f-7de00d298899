﻿namespace CommandGuard.Helpers;

public static class TimeHelper
{
    /// <summary>
    /// 格式化剩余时间显示 - 倒计时时间格式化工具
    ///
    /// 功能：
    /// - 将秒数转换为易读的时间格式
    /// - 支持负数时间显示
    /// - 统一的时间显示格式
    ///
    /// 格式规则：
    /// - 正数：显示为 HH:MM:SS 格式
    /// - 负数：显示为 -HH:MM:SS 格式
    /// - 自动补零确保格式一致
    ///
    /// 使用场景：
    /// - 开盘倒计时显示
    /// - 封盘倒计时显示
    /// - 其他时间相关的UI显示
    /// </summary>
    /// <param name="totalSeconds">剩余秒数（可以为负数）</param>
    /// <returns>格式化的时间字符串，格式为 HH:MM:SS 或 -HH:MM:SS</returns>
    public static string FormatTimeRemaining(int totalSeconds)
    {
        if (totalSeconds <= 0)
        {
            return @"00:00:00";
        }

        var timeSpan = TimeSpan.FromSeconds(totalSeconds);
        return $@"{timeSpan.Hours:D2}:{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
    }

}