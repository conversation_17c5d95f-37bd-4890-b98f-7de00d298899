using CommandGuard.Enums;
using CommandGuard.Models;
using CommandGuard.ViewModels;

namespace CommandGuard.Interfaces.Business;

/// <summary>
/// 投注记录服务接口
/// 提供投注记录的查询和统计功能
/// </summary>
public interface IBetRecordService
{
     #region 投注订单相关

    /// <summary>
    /// 创建投注订单
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <param name="nickName">用户昵称</param>
    /// <param name="issue">投注期号</param>
    /// <param name="playItem">投注项目</param>
    /// <param name="amount">投注金额</param>
    /// <param name="odds">投注赔率</param>
    /// <param name="originalMessage">原始消息内容</param>
    /// <param name="messageId">关联的消息ID</param>
    /// <returns>投注结果（订单ID和错误信息）</returns>
    Task<(long OrderId, string ErrorMessage)> CreateBetOrderAsync(string account, string nickName, string issue, string playItem, decimal amount, decimal odds, string originalMessage, string messageId);

    /// <summary>
    /// 确认投注订单
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="processor">处理人员</param>
    /// <param name="processNote">处理备注</param>
    /// <returns>是否操作成功</returns>
    Task<bool> ConfirmBetOrderAsync(long orderId, string processor, string processNote = "");

    /// <summary>
    /// 取消投注订单
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="processor">处理人员</param>
    /// <param name="processNote">取消原因</param>
    /// <returns>是否操作成功</returns>
    Task<bool> CancelBetOrderAsync(long orderId, string processor, string processNote = "");

    /// <summary>
    /// 拒绝投注订单
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="processor">处理人员</param>
    /// <param name="processNote">拒绝原因</param>
    /// <returns>是否操作成功</returns>
    Task<bool> RejectBetOrderAsync(long orderId, string processor, string processNote = "");

    /// <summary>
    /// 结算投注订单
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="settlementResult">结算结果（Win、Lose、Draw）</param>
    /// <param name="winAmount">中奖金额（中奖或和局时）</param>
    /// <param name="drawResult">开奖结果（番摊结果：1、2、3、4）</param>
    /// <param name="processor">处理人员</param>
    /// <returns>是否操作成功</returns>
    Task<bool> SettleBetOrderAsync(long orderId, EnumBetOrderStatus settlementResult, decimal winAmount, string drawResult, string processor);

    /// <summary>
    /// 派奖投注订单
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="processor">处理人员</param>
    /// <returns>是否操作成功</returns>
    Task<bool> PayoutBetOrderAsync(long orderId, string processor);

    /// <summary>
    /// 获取待处理的投注订单列表
    /// </summary>
    /// <returns>投注订单列表</returns>
    Task<List<BetOrder>> GetPendingBetOrdersAsync();

    /// <summary>
    /// 获取指定期号的投注订单列表
    /// </summary>
    /// <param name="issue">期号</param>
    /// <returns>投注订单列表</returns>
    Task<List<BetOrder>> GetBetOrdersByIssueAsync(string issue);

    /// <summary>
    /// 获取指定用户的投注订单列表
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <param name="limit">返回数量限制，0表示不限制</param>
    /// <returns>投注订单列表</returns>
    Task<List<BetOrder>> GetBetOrdersByAccountAsync(string account, int limit = 0);

    /// <summary>
    /// 获取指定用户和期号的投注订单
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <param name="issue">期号</param>
    /// <returns>投注订单列表</returns>
    Task<List<BetOrder>> GetBetOrdersByAccountAndIssueAsync(string account, string issue);

    /// <summary>
    /// 获取当前期的投注订单列表
    /// </summary>
    /// <param name="currentIssue">当前期号</param>
    /// <returns>当前期投注订单列表</returns>
    Task<List<BetOrder>> GetCurrentIssueBetOrdersAsync(string currentIssue);

    /// <summary>
    /// 获取当前期的增量投注订单列表（指定时间之后的数据）
    /// </summary>
    /// <param name="currentIssue">当前期号</param>
    /// <param name="lastUpdateTime">最后更新时间</param>
    /// <returns>增量投注订单列表</returns>
    Task<List<BetOrder>> GetCurrentIssueBetOrdersIncrementalAsync(string currentIssue, DateTime lastUpdateTime);

    /// <summary>
    /// 根据ID获取投注订单
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <returns>投注订单信息</returns>
    Task<BetOrder?> GetBetOrderAsync(long orderId);

    /// <summary>
    /// 获取所有未结算的投注订单列表
    /// </summary>
    /// <returns>未结算投注订单列表</returns>
    Task<List<BetOrder>> GetUnSettledBetOrdersAsync();

    /// <summary>
    /// 获取指定用户的所有未结算投注订单列表
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <returns>该用户的未结算投注订单列表</returns>
    Task<List<BetOrder>> GetUnSettledBetOrdersByAccountAsync(string account);

    // 移除数据修复方法，不再需要维护Member表中的UnsettledAmount字段

    #endregion

    #region 飞单状态管理

    /// <summary>
    /// 更新指定期号的所有投注订单的飞单状态
    /// </summary>
    /// <param name="issue">期号</param>
    /// <param name="enumFlightStatus">飞单状态</param>
    /// <param name="flightNote">飞单备注</param>
    /// <returns>更新成功的记录数</returns>
    Task<int> UpdateBetOrderFlightStatusByIssueAsync(string issue, EnumFlightOrderStatus enumFlightStatus, string flightNote = "");

    /// <summary>
    /// 更新单个投注订单的飞单状态
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="enumFlightStatus">飞单状态</param>
    /// <param name="flightNote">飞单备注</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateBetOrderFlightStatusAsync(long orderId, EnumFlightOrderStatus enumFlightStatus, string flightNote = "");

    #endregion

    /// <summary>
    /// 根据条件查询投注记录
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="issue">期号（可选）</param>
    /// <param name="account">账号（可选）</param>
    /// <param name="includeFakeUsers">是否包含假人</param>
    /// <returns>投注记录列表</returns>
    Task<List<BetRecordViewModel>> QueryBetRecordsAsync(
        DateTime startTime,
        DateTime endTime,
        string? issue = null,
        string? account = null,
        bool includeFakeUsers = false);

    /// <summary>
    /// 计算有效投注总额
    /// 有效投注是指开奖后赢了或输了的注单，和局的注单不计算在内
    /// </summary>
    /// <param name="betRecords">投注记录列表</param>
    /// <returns>有效投注总额</returns>
    decimal CalculateValidBetAmount(List<BetRecordViewModel> betRecords);

    /// <summary>
    /// 计算总盈亏
    /// 正数表示总盈利，负数表示总亏损
    /// </summary>
    /// <param name="betRecords">投注记录列表</param>
    /// <returns>总盈亏金额</returns>
    decimal CalculateTotalProfitLoss(List<BetRecordViewModel> betRecords);

    /// <summary>
    /// 获取投注记录统计信息
    /// </summary>
    /// <param name="betRecords">投注记录列表</param>
    /// <returns>统计信息元组（有效投注总额，总盈亏）</returns>
    (decimal ValidBetAmount, decimal TotalProfitLoss) GetBetRecordStatistics(List<BetRecordViewModel> betRecords);

    /// <summary>
    /// 获取区分真假订单的投注记录统计信息
    /// </summary>
    /// <param name="betRecords">投注记录列表</param>
    /// <returns>统计信息元组（真订单有效投注总额，真订单总盈亏，假订单有效投注总额，假订单总盈亏）</returns>
    (decimal RealValidBetAmount, decimal RealTotalProfitLoss, decimal FakeValidBetAmount, decimal FakeTotalProfitLoss) GetBetRecordStatisticsByOrderType(List<BetRecordViewModel> betRecords);

    /// <summary>
    /// 获取用户的有效流水总额
    /// 查询该用户所有已结算（输或赢）的历史投注记录，计算投注总额
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <returns>该用户的有效流水总额</returns>
    Task<decimal> GetUserValidTurnoverAsync(string account);

    /// <summary>
    /// 获取用户未执行回水操作的有效流水总额
    /// 查询该用户所有已结算（输或赢）且未执行过回水的历史投注记录，计算投注总额
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <returns>该用户未执行回水操作的有效流水总额</returns>
    Task<decimal> GetUserUnpaidRebateValidTurnoverAsync(string account);

    /// <summary>
    /// 获取指定用户指定期号的投注记录（按投注项目合并）
    /// 查询该用户在指定期号的所有投注记录，按投注项目分组并合并投注金额
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <param name="issue">期号</param>
    /// <returns>按投注项目分组的投注记录列表</returns>
    Task<List<BetItemSummary>> GetUserBetSummaryByIssueAsync(string account, string issue);

    /// <summary>
    /// 获取指定会员的当前盈亏
    /// 查询该会员所有已结算的投注记录（输状态或赢状态），计算中奖金额总和减去投注额总和
    /// </summary>
    /// <param name="account">会员账号</param>
    /// <returns>会员的当前盈亏（正数为盈利，负数为亏损）</returns>
    Task<decimal> GetUserCurrentProfitLossAsync(string account);

    /// <summary>
    /// 获取用户在指定期号的投注总额
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <param name="issue">期号</param>
    /// <returns>投注总额</returns>
    Task<decimal> GetUserCurrentIssueBetAmountAsync(string account, string issue);

    /// <summary>
    /// 获取指定期号和投注项目的总投注额
    /// 用于检查投注项目的总限额控制
    /// </summary>
    /// <param name="issue">期号</param>
    /// <param name="playItem">投注项目</param>
    /// <returns>该投注项目在指定期号的总投注额</returns>
    Task<decimal> GetCurrentIssuePlayItemTotalBetAsync(string issue, string playItem);
}