namespace CommandGuard.Enums;

/// <summary>
/// 投注结果枚举
/// 定义投注的输赢状态
/// </summary>
public enum EnumBetResult
{
    /// <summary>
    /// 输 - 投注失败，扣除投注金额
    /// </summary>
    Loss = 0,

    /// <summary>
    /// 赢 - 投注成功，按赔率获得奖金
    /// </summary>
    Win = 1,

    /// <summary>
    /// 和局 - 平局，退还投注金额
    /// </summary>
    Draw = 2
}

// /// <summary>
// /// 投注结果枚举扩展方法
// /// </summary>
// public static class BetResultExtensions
// {
//     /// <summary>
//     /// 获取投注结果的中文描述
//     /// </summary>
//     /// <param name="result">投注结果</param>
//     /// <returns>中文描述</returns>
//     public static string GetDescription(this BetResult result)
//     {
//         return result switch
//         {
//             BetResult.Win => "中奖",
//             BetResult.Loss => "不中奖",
//             BetResult.Draw => "和局",
//             _ => "未知"
//         };
//     }
//
//     /// <summary>
//     /// 判断是否为中奖结果
//     /// </summary>
//     /// <param name="result">投注结果</param>
//     /// <returns>是否中奖</returns>
//     public static bool IsWin(this BetResult result)
//     {
//         return result == BetResult.Win;
//     }
//
//     /// <summary>
//     /// 判断是否为和局结果
//     /// </summary>
//     /// <param name="result">投注结果</param>
//     /// <returns>是否和局</returns>
//     public static bool IsDraw(this BetResult result)
//     {
//         return result == BetResult.Draw;
//     }
//
//     /// <summary>
//     /// 判断是否为输的结果
//     /// </summary>
//     /// <param name="result">投注结果</param>
//     /// <returns>是否输</returns>
//     public static bool IsLoss(this BetResult result)
//     {
//         return result == BetResult.Loss;
//     }
//
//     /// <summary>
//     /// 计算结算金额
//     /// </summary>
//     /// <param name="result">投注结果</param>
//     /// <param name="betAmount">投注金额</param>
//     /// <param name="odds">赔率</param>
//     /// <returns>结算金额（正数为获得，负数为扣除，0为不变）</returns>
//     public static decimal CalculateSettlementAmount(this BetResult result, decimal betAmount, decimal odds)
//     {
//         return result switch
//         {
//             BetResult.Win => betAmount * odds,      // 中奖：投注金额 × 赔率
//             BetResult.Loss => -betAmount,           // 输：扣除投注金额
//             BetResult.Draw => 0,                    // 和局：不扣不加
//             _ => -betAmount                         // 默认为输
//         };
//     }
// }
