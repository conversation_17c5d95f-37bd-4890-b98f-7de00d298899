{"version": 3, "targets": {"net8.0-windows7.0": {"System.CodeDom/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Management/8.0.0": {"type": "package", "dependencies": {"System.CodeDom": "8.0.0"}, "compile": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}}}, "libraries": {"System.CodeDom/8.0.0": {"sha512": "WTlRjL6KWIMr/pAaq3rYqh0TJlzpouaQ/W1eelssHgtlwHAH25jXTkUphTYx9HaIIf7XA6qs/0+YhtLEQRkJ+Q==", "type": "package", "path": "system.codedom/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net6.0/System.CodeDom.dll", "lib/net6.0/System.CodeDom.xml", "lib/net7.0/System.CodeDom.dll", "lib/net7.0/System.CodeDom.xml", "lib/net8.0/System.CodeDom.dll", "lib/net8.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.8.0.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Management/8.0.0": {"sha512": "jrK22i5LRzxZCfGb+tGmke2VH7oE0DvcDlJ1HAKYU8cPmD8XnpUT0bYn2Gy98GEhGjtfbR/sxKTVb+dE770pfA==", "type": "package", "path": "system.management/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net6.0/System.Management.dll", "lib/net6.0/System.Management.xml", "lib/net7.0/System.Management.dll", "lib/net7.0/System.Management.xml", "lib/net8.0/System.Management.dll", "lib/net8.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net6.0/System.Management.dll", "runtimes/win/lib/net6.0/System.Management.xml", "runtimes/win/lib/net7.0/System.Management.dll", "runtimes/win/lib/net7.0/System.Management.xml", "runtimes/win/lib/net8.0/System.Management.dll", "runtimes/win/lib/net8.0/System.Management.xml", "system.management.8.0.0.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["System.Management >= 8.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\RobotCopy\\SolutionCommandGuard\\CommandGuard.Licensing\\CommandGuard.Licensing.csproj", "projectName": "CommandGuard.<PERSON>", "projectPath": "F:\\RobotCopy\\SolutionCommandGuard\\CommandGuard.Licensing\\CommandGuard.Licensing.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\RobotCopy\\SolutionCommandGuard\\CommandGuard.Licensing\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"System.Management": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}