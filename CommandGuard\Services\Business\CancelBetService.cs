using CommandGuard.Enums;
using CommandGuard.Helpers;
using CommandGuard.Interfaces.Business;
using CommandGuard.Interfaces.Chat;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Business;

/// <summary>
/// 撤销投注服务
/// 提供统一的投注撤销和退款功能
/// </summary>
public class CancelBetService
{
    #region 字段和属性

    private readonly ILogger<CancelBetService> _logger;
    private readonly IFreeSql _fSql;
    private readonly IFinancialService _financialService;
    private readonly IChatService _chatService;

    #endregion

    #region 构造函数

    public CancelBetService(
        ILogger<CancelBetService> logger,
        IFreeSql fSql,
        IFinancialService financialService,
        IChatService chatService)
    {
        _logger = logger;
        _fSql = fSql;
        _financialService = financialService;
        _chatService = chatService;
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 按期号撤销所有投注并退款
    /// </summary>
    /// <param name="issue">期号</param>
    /// <param name="operatorName">操作员名称</param>
    /// <param name="sendNotification">是否发送群聊通知</param>
    /// <returns>撤销结果</returns>
    public async Task<CancelBetResult> CancelBetsByIssueAsync(string issue, string operatorName = "系统", bool sendNotification = true)
    {
        try
        {
            _logger.LogInformation(@"开始按期号撤销投注，期号：{Issue}，操作员：{Operator}", issue, operatorName);

            // 1. 获取该期所有未结算的投注
            var unsettledBets = await _fSql.Select<BetOrder>()
                .Where(b => b.Issue == issue && b.Status == EnumBetOrderStatus.Confirmed)
                .ToListAsync();

            if (!unsettledBets.Any())
            {
                _logger.LogWarning(@"期号 {Issue} 没有找到未结算的投注", issue);
                return new CancelBetResult
                {
                    Success = true,
                    Message = $@"期号 {issue} 没有未结算的投注",
                    CancelledOrderCount = 0,
                    TotalRefundAmount = 0
                };
            }

            _logger.LogInformation(@"获取到 {Count} 个未结算投注需要撤销，期号：{Issue}", unsettledBets.Count, issue);

            var cancelledCount = 0;
            var totalRefundAmount = 0m;
            var userRefunds = new Dictionary<string, decimal>();

            // 2. 逐个撤销投注并退款
            foreach (var bet in unsettledBets)
            {
                try
                {
                    // 更新投注状态为已取消
                    bet.Status = EnumBetOrderStatus.Cancelled;
                    bet.SettledTime = DateTime.Now;

                    await _fSql.Update<BetOrder>()
                        .SetSource(bet)
                        .ExecuteAffrowsAsync();

                    // 退还投注金额
                    await _financialService.IncreaseBalanceAsync(
                        bet.Account,
                        bet.Amount,
                        @"BetRefund",
                        bet.Id,
                        $@"期号{issue}投注撤销退款",
                        operatorName);

                    cancelledCount++;
                    totalRefundAmount += bet.Amount;

                    // 统计每个用户的退款金额
                    if (userRefunds.ContainsKey(bet.Account))
                    {
                        userRefunds[bet.Account] += bet.Amount;
                    }
                    else
                    {
                        userRefunds[bet.Account] = bet.Amount;
                    }

                    _logger.LogDebug(@"撤销投注完成，订单ID：{OrderId}，用户：{Account}，退款金额：{RefundAmount}",
                        bet.Id, bet.Account, bet.Amount);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, @"撤销单个投注失败，订单ID：{OrderId}", bet.Id);
                    // 继续处理其他订单，不因单个失败而中断
                }
            }

            // 3. 发送群聊通知（如果需要）
            if (sendNotification && userRefunds.Any())
            {
                await SendIssueRefundNotificationAsync(issue, userRefunds, cancelledCount);
            }

            var result = new CancelBetResult
            {
                Success = true,
                Message = $@"期号 {issue} 撤销完成，共撤销 {cancelledCount} 个订单，退还 {totalRefundAmount:F2}",
                CancelledOrderCount = cancelledCount,
                TotalRefundAmount = totalRefundAmount,
                AffectedUsers = userRefunds.Keys.ToList()
            };

            _logger.LogInformation(@"按期号撤销投注完成，期号：{Issue}，撤销订单数：{Count}，退款总额：{Amount:F2}",
                issue, cancelledCount, totalRefundAmount);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"按期号撤销投注失败，期号：{Issue}", issue);
            return new CancelBetResult
            {
                Success = false,
                Message = $@"撤销失败：{ex.Message}",
                CancelledOrderCount = 0,
                TotalRefundAmount = 0
            };
        }
    }

    /// <summary>
    /// 按用户账号撤销投注并退款
    /// </summary>
    /// <param name="accounts">用户账号列表</param>
    /// <param name="operatorName">操作员名称</param>
    /// <param name="sendNotification">是否发送群聊通知</param>
    /// <returns>撤销结果</returns>
    public async Task<CancelBetResult> CancelBetsByAccountsAsync(List<string> accounts, string operatorName = "管理员", bool sendNotification = true)
    {
        try
        {
            _logger.LogInformation(@"开始按用户撤销投注，用户数：{Count}，操作员：{Operator}", accounts.Count, operatorName);

            var totalCancelledCount = 0;
            var totalRefundAmount = 0m;
            var userResults = new Dictionary<string, (int count, decimal amount, List<string> issues)>();

            foreach (var account in accounts)
            {
                try
                {
                    // 获取用户的未结算投注
                    var unsettledBets = await _fSql.Select<BetOrder>()
                        .Where(b => b.Account == account && b.Status == EnumBetOrderStatus.Confirmed)
                        .ToListAsync();

                    if (!unsettledBets.Any())
                    {
                        _logger.LogWarning(@"用户 {Account} 没有未结算的投注", account);
                        continue;
                    }

                    var userCancelledCount = 0;
                    var userRefundAmount = 0m;
                    var userIssues = new List<string>();

                    // 撤销用户的所有未结算投注
                    foreach (var bet in unsettledBets)
                    {
                        // 更新投注状态为已取消
                        bet.Status = EnumBetOrderStatus.Cancelled;
                        bet.SettledTime = DateTime.Now;

                        await _fSql.Update<BetOrder>()
                            .SetSource(bet)
                            .ExecuteAffrowsAsync();

                        // 退还投注金额
                        await _financialService.IncreaseBalanceAsync(
                            bet.Account,
                            bet.Amount,
                            @"BetRefund",
                            bet.Id,
                            @"管理员批量撤单",
                            operatorName);

                        userCancelledCount++;
                        userRefundAmount += bet.Amount;

                        if (!userIssues.Contains(bet.Issue))
                        {
                            userIssues.Add(bet.Issue);
                        }
                    }

                    totalCancelledCount += userCancelledCount;
                    totalRefundAmount += userRefundAmount;
                    userResults[account] = (userCancelledCount, userRefundAmount, userIssues);

                    // 发送单个用户的撤单通知
                    if (sendNotification && userCancelledCount > 0)
                    {
                        await SendUserRefundNotificationAsync(account, userCancelledCount, userRefundAmount, userIssues);
                    }

                    _logger.LogInformation(@"用户 {Account} 撤单完成，撤销 {Count} 个订单，退还 {Amount:F2}",
                        account, userCancelledCount, userRefundAmount);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, @"为用户 {Account} 撤单时发生异常", account);
                    // 继续处理其他用户
                }
            }

            var result = new CancelBetResult
            {
                Success = true,
                Message = $@"批量撤单完成，共撤销 {totalCancelledCount} 个订单，退还 {totalRefundAmount:F2}",
                CancelledOrderCount = totalCancelledCount,
                TotalRefundAmount = totalRefundAmount,
                AffectedUsers = userResults.Keys.ToList()
            };

            _logger.LogInformation(@"按用户撤销投注完成，撤销订单数：{Count}，退款总额：{Amount:F2}",
                totalCancelledCount, totalRefundAmount);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"按用户撤销投注失败");
            return new CancelBetResult
            {
                Success = false,
                Message = $@"撤销失败：{ex.Message}",
                CancelledOrderCount = 0,
                TotalRefundAmount = 0
            };
        }
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 发送期号退款通知到群聊
    /// </summary>
    private async Task SendIssueRefundNotificationAsync(string issue, Dictionary<string, decimal> userRefunds, int totalOrders)
    {
        try
        {
            var totalAmount = userRefunds.Values.Sum();
            var userCount = userRefunds.Count;

            var notificationMessage = $@"{EmojiHelper.GetCrossMark()}期号撤单通知{EmojiHelper.GetCrossMark()}" + Environment.NewLine +
                                      $@"题号：{issue}" + Environment.NewLine +
                                      $@"撤销：{totalOrders}个答题" + Environment.NewLine +
                                      $@"涉及：{userCount}个用户" + Environment.NewLine +
                                      $@"返还：{EmojiHelper.GetMoneyBag()}{totalAmount:F2}" + Environment.NewLine;

            await _chatService.SendGroupMessageAsync(notificationMessage);

            _logger.LogInformation(@"期号撤单通知已发送到群聊，期号: {Issue}, 订单数: {Count}, 金额: {Amount:F2}",
                issue, totalOrders, totalAmount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"发送期号撤单通知失败，期号: {Issue}", issue);
        }
    }

    /// <summary>
    /// 发送用户退款通知到群聊
    /// </summary>
    private async Task SendUserRefundNotificationAsync(string account, int orderCount, decimal refundAmount, List<string> issues)
    {
        try
        {
            var issuesText = string.Join("、", issues.Take(3));
            if (issues.Count > 3)
            {
                issuesText += "等";
            }

            var notificationMessage = $@"{EmojiHelper.GetCrossMark()}撤单通知{EmojiHelper.GetCrossMark()}" + Environment.NewLine +
                                      $@"题号：{issuesText}" + Environment.NewLine +
                                      $@"撤销：{orderCount}个答题" + Environment.NewLine +
                                      $@"返还：{EmojiHelper.GetMoneyBag()}{refundAmount:F2}" + Environment.NewLine;

            await _chatService.SendGroupMessageAsync(notificationMessage, account);

            _logger.LogInformation(@"用户撤单通知已发送到群聊，用户: {Account}, 订单数: {Count}, 金额: {Amount:F2}",
                account, orderCount, refundAmount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"发送用户撤单通知失败，用户: {Account}", account);
        }
    }

    #endregion
}

/// <summary>
/// 撤销投注结果
/// </summary>
public class CancelBetResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 结果消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 撤销的订单数量
    /// </summary>
    public int CancelledOrderCount { get; set; }

    /// <summary>
    /// 退款总金额
    /// </summary>
    public decimal TotalRefundAmount { get; set; }

    /// <summary>
    /// 受影响的用户列表
    /// </summary>
    public List<string> AffectedUsers { get; set; } = [];
}
