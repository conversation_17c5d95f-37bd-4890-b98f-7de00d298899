﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>WinExe</OutputType>
		<TargetFramework>net8.0-windows</TargetFramework>
		<Nullable>enable</Nullable>
		<UseWindowsForms>true</UseWindowsForms>
		<ImplicitUsings>enable</ImplicitUsings>
		<ApplicationIcon>Ulysses.ico</ApplicationIcon>
	</PropertyGroup>

	<ItemGroup>
		<Content Include="Ulysses.ico" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\CommandGuard.Licensing\CommandGuard.Licensing.csproj" />
	</ItemGroup>

</Project>