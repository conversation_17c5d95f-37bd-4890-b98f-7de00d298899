using Microsoft.Extensions.Logging;
using System.Media;

namespace CommandGuard.Services.Infrastructure;

/// <summary>
/// 音频播放服务接口
/// 提供系统各种场景下的音效播放功能
/// </summary>
public interface IAudioService
{
    /// <summary>
    /// 播放服务启动音效
    /// </summary>
    Task PlayStartServiceAsync();

    /// <summary>
    /// 播放服务停止音效
    /// </summary>
    Task PlayStopServiceAsync();

    /// <summary>
    /// 播放充值提现音效
    /// </summary>
    Task PlayRechargeWithdrawalAsync();

    /// <summary>
    /// 播放停止答题音效
    /// </summary>
    Task PlayStopBetAsync();

    /// <summary>
    /// 播放答题成功音效
    /// </summary>
    Task PlayBetSuccessAsync();

    /// <summary>
    /// 播放答题失败音效
    /// </summary>
    Task PlayBetFailureAsync();

    /// <summary>
    /// 播放开奖开始答题音效
    /// </summary>
    Task PlayOpenDrawAsync();

    /// <summary>
    /// 设置音频是否启用
    /// </summary>
    /// <param name="enabled">是否启用音频</param>
    Task SetAudioEnabledAsync(bool enabled);

    /// <summary>
    /// 获取音频是否启用
    /// </summary>
    /// <returns>是否启用音频</returns>
    Task<bool> IsAudioEnabledAsync();
}

/// <summary>
/// 音频播放服务实现
/// 负责播放系统各种场景下的音效文件
/// 
/// 功能特点：
/// - 支持异步播放，不阻塞主线程
/// - 自动检查音频文件是否存在
/// - 支持音频开关控制
/// - 完善的错误处理和日志记录
/// - 线程安全的音频播放
/// </summary>
public class AudioService : IAudioService
{
    #region 私有字段

    private readonly ILogger<AudioService> _logger;
    private readonly string _mediaDirectory;
    private bool _audioEnabled = true;
    private readonly object _lockObject = new();

    // 音频文件路径常量
    private readonly Dictionary<string, string> _audioFiles;

    #endregion

    #region 构造函数

    /// <summary>
    /// 音频服务构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public AudioService(ILogger<AudioService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // 音频文件目录
        _mediaDirectory = Path.Combine(Application.StartupPath, "media");
        
        // 初始化音频文件路径字典
        _audioFiles = new Dictionary<string, string>
        {
            ["StartService"] = Path.Combine(_mediaDirectory, "StartService.wav"),
            ["StopService"] = Path.Combine(_mediaDirectory, "StopService.wav"),
            ["RechargeWithdrawal"] = Path.Combine(_mediaDirectory, "RechargeWithdrawal.wav"),
            ["StopBet"] = Path.Combine(_mediaDirectory, "StopBet.wav"),
            ["BetSuccess"] = Path.Combine(_mediaDirectory, "BetSuccess.wav"),
            ["BetFalse"] = Path.Combine(_mediaDirectory, "BetFalse.wav"),
            ["OpenDraw"] = Path.Combine(_mediaDirectory, "OpenDraw.wav")
        };

        _logger.LogDebug(@"音频服务已初始化，媒体目录: {MediaDirectory}", _mediaDirectory);
        
        // 检查音频文件是否存在
        CheckAudioFiles();
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 播放服务启动音效
    /// </summary>
    public async Task PlayStartServiceAsync()
    {
        await PlayAudioAsync("StartService", "服务启动");
    }

    /// <summary>
    /// 播放服务停止音效
    /// </summary>
    public async Task PlayStopServiceAsync()
    {
        await PlayAudioAsync("StopService", "服务停止");
    }

    /// <summary>
    /// 播放充值提现音效
    /// </summary>
    public async Task PlayRechargeWithdrawalAsync()
    {
        await PlayAudioAsync("RechargeWithdrawal", "充值提现");
    }

    /// <summary>
    /// 播放停止答题音效
    /// </summary>
    public async Task PlayStopBetAsync()
    {
        await PlayAudioAsync("StopBet", "停止答题");
    }

    /// <summary>
    /// 播放答题成功音效
    /// </summary>
    public async Task PlayBetSuccessAsync()
    {
        await PlayAudioAsync("BetSuccess", "答题成功");
    }

    /// <summary>
    /// 播放答题失败音效
    /// </summary>
    public async Task PlayBetFailureAsync()
    {
        await PlayAudioAsync("BetFalse", "答题失败");
    }

    /// <summary>
    /// 播放开奖开始答题音效
    /// </summary>
    public async Task PlayOpenDrawAsync()
    {
        await PlayAudioAsync("OpenDraw", "开奖开始答题");
    }

    /// <summary>
    /// 设置音频是否启用
    /// </summary>
    /// <param name="enabled">是否启用音频</param>
    public async Task SetAudioEnabledAsync(bool enabled)
    {
        lock (_lockObject)
        {
            _audioEnabled = enabled;
        }
        
        _logger.LogInformation(@"音频播放已{Status}", enabled ? "启用" : "禁用");
        await Task.CompletedTask;
    }

    /// <summary>
    /// 获取音频是否启用
    /// </summary>
    /// <returns>是否启用音频</returns>
    public async Task<bool> IsAudioEnabledAsync()
    {
        await Task.CompletedTask;
        lock (_lockObject)
        {
            return _audioEnabled;
        }
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 播放指定的音频文件
    /// </summary>
    /// <param name="audioKey">音频文件键名</param>
    /// <param name="description">音频描述</param>
    private async Task PlayAudioAsync(string audioKey, string description)
    {
        try
        {
            // 检查音频是否启用
            if (!await IsAudioEnabledAsync())
            {
                _logger.LogDebug(@"音频已禁用，跳过播放: {Description}", description);
                return;
            }

            // 获取音频文件路径
            if (!_audioFiles.TryGetValue(audioKey, out var audioPath))
            {
                _logger.LogWarning(@"未找到音频文件配置: {AudioKey}", audioKey);
                return;
            }

            // 检查文件是否存在
            if (!File.Exists(audioPath))
            {
                _logger.LogWarning(@"音频文件不存在: {AudioPath}", audioPath);
                return;
            }

            // 在后台线程播放音频，避免阻塞主线程
            await Task.Run(() =>
            {
                try
                {
                    using var soundPlayer = new SoundPlayer(audioPath);
                    soundPlayer.PlaySync(); // 同步播放，确保音频播放完成
                    
                    _logger.LogDebug(@"音频播放完成: {Description} - {AudioPath}", description, audioPath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, @"播放音频文件失败: {AudioPath}", audioPath);
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"播放音频时发生异常: {Description}", description);
        }
    }

    /// <summary>
    /// 检查音频文件是否存在
    /// </summary>
    private void CheckAudioFiles()
    {
        try
        {
            var missingFiles = new List<string>();
            var existingFiles = new List<string>();

            foreach (var kvp in _audioFiles)
            {
                if (File.Exists(kvp.Value))
                {
                    existingFiles.Add(kvp.Key);
                }
                else
                {
                    missingFiles.Add(kvp.Key);
                }
            }

            if (existingFiles.Count > 0)
            {
                _logger.LogInformation(@"找到音频文件: {ExistingFiles}", string.Join(", ", existingFiles));
            }

            if (missingFiles.Count > 0)
            {
                _logger.LogWarning(@"缺少音频文件: {MissingFiles}", string.Join(", ", missingFiles));
            }

            _logger.LogInformation(@"音频文件检查完成，存在: {ExistingCount}/{TotalCount}", 
                existingFiles.Count, _audioFiles.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"检查音频文件时发生异常");
        }
    }

    #endregion
}
