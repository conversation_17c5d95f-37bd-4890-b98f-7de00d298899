﻿using CommandGuard.Models;

namespace CommandGuard.Interfaces.Lottery;

/// <summary>
/// 期号时间服务接口
/// </summary>
public interface IIssueTimeService : IDisposable
{
    /// <summary>
    /// 创建期号时间数据
    /// </summary>
    /// <returns>创建的第一条期号时间记录</returns>
    Task<IssueTime> CreateIssueTimeAsync();

    /// <summary>
    /// 维护更新当前期号时间信息
    /// </summary>
    /// <param name="token">取消令牌，用于优雅停止服务</param>
    /// <returns>持续运行的异步任务</returns>
    Task UpdateCurrentIssueTimeAsync(CancellationToken token);

    /// <summary>
    /// 线程安全地获取当前缓存的期号时间信息
    /// </summary>
    /// <returns>当前缓存的期号时间对象，缓存为空时返回null</returns>
    IssueTime? GetCurrentCachedIssueTime();

    int GetCurrentCachedOpenTimeSpan();
    int GetCurrentCachedCloseTimeSpan();
    bool IsFirstIssueOfDay(IssueTime issueTime);
    
    /// <summary>
    /// 清空期号时间信息 - 危险操作
    ///
    /// 功能：
    /// - 删除IssueTime表中的所有期号时间记录
    /// - 清空期号生成和时间管理的基础数据
    ///
    /// ⚠️ 警告：
    /// - 危险操作，会删除所有期号时间数据
    /// - 执行后需要重新生成期号时间数据
    ///
    /// 使用场景：
    /// - 系统初始化、数据重置、测试环境清理
    /// </summary>
    Task ClearIssueTimeAsync(CancellationToken token);

    /// <summary>
    /// 清除期号时间缓存
    /// </summary>
    void ClearCache();

    /// <summary>
    /// 获取上期期号信息 - 用于开盘前置条件检查
    ///
    /// 功能：
    /// - 通过字符串比较查找比当前期号小的最大期号
    /// - 支持异步操作和取消令牌
    /// - 用于检查上期开奖状态
    ///
    /// 业务场景：
    /// - 开盘前检查上期是否已开奖
    /// - 确保开盘流程的完整性
    /// </summary>
    /// <param name="currentIssue">当前期号</param>
    /// <param name="token">取消令牌</param>
    /// <returns>上期期号信息，如果不存在则返回null</returns>
    Task<IssueTime?> GetPreviousIssueAsync(string currentIssue, CancellationToken token);
}