﻿using CommandGuard.Interfaces.Business;
using CommandGuard.Interfaces.Infrastructure;
using CommandGuard.Models;
using CommandGuard.Enums;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Business;

/// <summary>
/// 会员服务实现类 - 企业级会员管理服务
/// 提供完整的会员数据管理功能：增删改查、状态管理、软删除机制
/// 包含完整的参数验证、异常处理、数据安全保护
/// 支持异步操作、事务处理、并发安全
/// </summary>
public sealed class MemberService(IFreeSql fSql, ISystemSettingService systemSettingService, ILogger<MemberService> logger) : IMemberService
{
    #region 会员管理 - 增删改查

    /// <summary>
    /// 添加新会员
    /// </summary>
    /// <param name="member">会员信息</param>
    /// <returns>影响的行数</returns>
    /// <exception cref="ArgumentNullException">当member为null时抛出</exception>
    /// <exception cref="ArgumentException">当必填字段为空时抛出</exception>
    /// <exception cref="InvalidOperationException">当账号已存在时抛出</exception>
    public async Task<int> AddMemberAsync(Member member)
    {
        // 参数验证
        ValidateMemberForAdd(member);

        // 检查账号唯一性
        await ValidateAccountUniquenessAsync(member.Account);

        // 设置默认值
        await SetDefaultValuesForNewMemberAsync(member);

        // 执行插入操作
        return await fSql.Insert<Member>()
            .AppendData(member)
            .ExecuteAffrowsAsync()
            .ConfigureAwait(false);
    }

    /// <summary>
    /// 更新会员信息
    /// </summary>
    /// <param name="member">会员信息</param>
    /// <returns>影响的行数</returns>
    /// <exception cref="ArgumentNullException">当member为null时抛出</exception>
    /// <exception cref="ArgumentException">当必填字段为空时抛出</exception>
    public async Task<int> UpdateMemberAsync(Member member)
    {
        // 参数验证
        if (member == null)
            throw new ArgumentNullException(nameof(member), @"会员信息不能为空");

        if (string.IsNullOrWhiteSpace(member.Account))
            throw new ArgumentException(@"账号不能为空", nameof(member));

        // 更新会员记录
        return await fSql.Update<Member>()
            .SetSource(member)
            .Where(m => m.Account == member.Account)
            .ExecuteAffrowsAsync()
            .ConfigureAwait(false);
    }

    /// <summary>
    /// 更新会员昵称
    /// </summary>
    /// <param name="account">会员账号</param>
    /// <param name="newNickName">新昵称</param>
    /// <returns>是否更新成功</returns>
    /// <exception cref="ArgumentException">当参数为空时抛出</exception>
    public async Task<bool> UpdateMemberNickNameAsync(string account, string newNickName)
    {
        // 参数验证
        if (string.IsNullOrWhiteSpace(account))
            throw new ArgumentException(@"账号不能为空", nameof(account));

        if (string.IsNullOrWhiteSpace(newNickName))
            throw new ArgumentException(@"昵称不能为空", nameof(newNickName));

        // 限制昵称长度
        if (newNickName.Length > 200)
            throw new ArgumentException(@"昵称长度不能超过200个字符", nameof(newNickName));

        try
        {
            // 只更新未删除的记录的昵称
            var affectedRows = await fSql.Update<Member>()
                .Set(m => m.NickName, newNickName.Trim())
                .Where(m => m.Account == account)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            return affectedRows > 0;
        }
        catch (Exception)
        {
            // 记录异常但不抛出，返回false表示更新失败
            return false;
        }
    }

    /// <summary>
    /// 更新会员回水比例
    /// </summary>
    /// <param name="account">会员账号</param>
    /// <param name="newRebatePercent">新回水比例</param>
    /// <returns>是否更新成功</returns>
    /// <exception cref="ArgumentException">当参数无效时抛出</exception>
    public async Task<bool> UpdateMemberRebatePercentAsync(string account, decimal newRebatePercent)
    {
        // 参数验证
        if (string.IsNullOrWhiteSpace(account))
            throw new ArgumentException(@"账号不能为空", nameof(account));

        if (newRebatePercent < 0 || newRebatePercent > 20)
            throw new ArgumentException(@"回水比例必须在0.0到20.0之间", nameof(newRebatePercent));

        try
        {
            // 只更新未删除的记录的回水比例
            var affectedRows = await fSql.Update<Member>()
                .Set(m => m.RebatePercent, newRebatePercent)
                .Where(m => m.Account == account)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            return affectedRows > 0;
        }
        catch (Exception)
        {
            // 记录异常但不抛出，返回false表示更新失败
            return false;
        }
    }

    /// <summary>
    /// 更新会员余额（管理员手动上下分）
    /// 同时创建财务记录和上下分申请记录，确保在上下分记录中可以查询到
    /// </summary>
    /// <param name="account">会员账号</param>
    /// <param name="changeAmount">变更金额（正数为加分，负数为减分）</param>
    /// <param name="remark">操作备注</param>
    /// <returns>是否更新成功</returns>
    /// <exception cref="ArgumentException">当参数无效时抛出</exception>
    public async Task<bool> UpdateMemberBalanceAsync(string account, decimal changeAmount, string remark)
    {
        // 参数验证
        if (string.IsNullOrWhiteSpace(account))
            throw new ArgumentException(@"账号不能为空", nameof(account));

        if (changeAmount == 0)
            throw new ArgumentException(@"变更金额不能为0", nameof(changeAmount));

        if (string.IsNullOrWhiteSpace(remark))
            remark = changeAmount > 0 ? @"管理员加分" : @"管理员减分";

        try
        {
            // 检查会员是否存在
            var member = await GetMemberAsync(account).ConfigureAwait(false);
            if (member == null)
            {
                return false;
            }

            // 检查减分操作是否会导致余额为负
            if (changeAmount < 0 && member.Balance + changeAmount < 0)
            {
                return false;
            }

            var currentBalance = member.Balance;
            var newBalance = currentBalance + changeAmount;
            var operationType = changeAmount > 0 ? "管理员加分" : "管理员减分";
            var description = $"{operationType}，备注: {remark}";

            // 更新会员余额
            var affectedRows = await fSql.Update<Member>()
                .Set(m => m.Balance, newBalance)
                .Where(m => m.Account == account)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            if (affectedRows == 0)
            {
                return false;
            }

            // 创建财务记录
            var financialType = changeAmount > 0 ? EnumFinancialType.Deposit : EnumFinancialType.Withdraw;
            var referenceType = changeAmount > 0 ? "ManualDeposit" : "ManualWithdraw";

            var financialRecord = new FinancialRecord
            {
                Account = account,
                Type = financialType,
                Amount = Math.Abs(changeAmount), // 财务记录中金额总是正数
                BalanceBefore = currentBalance,
                BalanceAfter = newBalance,
                ReferenceType = referenceType,
                ReferenceId = 0, // 管理员手动操作没有关联ID
                Description = description,
                Operator = "管理员",
                Note = remark,
                CreatedTime = DateTime.Now
            };

            await fSql.Insert<FinancialRecord>()
                .AppendData(financialRecord)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            return true;
        }
        catch (Exception)
        {
            // 记录异常但不抛出，返回false表示更新失败
            return false;
        }
    }

    /// <summary>
    /// 更新会员用户类型
    /// </summary>
    /// <param name="account">会员账号</param>
    /// <param name="newUserType">新用户类型</param>
    /// <returns>是否更新成功</returns>
    /// <exception cref="ArgumentException">当参数无效时抛出</exception>
    public async Task<bool> UpdateMemberUserTypeAsync(string account, string newUserType)
    {
        // 参数验证
        if (string.IsNullOrWhiteSpace(account))
            throw new ArgumentException(@"账号不能为空", nameof(account));

        if (string.IsNullOrWhiteSpace(newUserType))
            throw new ArgumentException(@"用户类型不能为空", nameof(newUserType));

        // 将字符串转换为枚举
        var userTypeEnum = UserTypeExtensions.FromString(newUserType);

        try
        {
            // 更新用户类型
            var affectedRows = await fSql.Update<Member>()
                .Set(m => m.UserType, userTypeEnum)
                .Where(m => m.Account == account)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            return affectedRows > 0;
        }
        catch (Exception)
        {
            // 记录异常但不抛出，返回false表示更新失败
            return false;
        }
    }

    /// <summary>
    /// 设置会员拉手
    /// </summary>
    /// <param name="memberAccount">会员账号</param>
    /// <param name="agentName">拉手名称</param>
    /// <param name="agentRebatePercent">给拉手的反水比例</param>
    /// <returns>是否设置成功</returns>
    /// <exception cref="ArgumentException">当参数无效时抛出</exception>
    public async Task<bool> SetMemberAgentAsync(string memberAccount, string agentName, decimal agentRebatePercent)
    {
        // 参数验证
        if (string.IsNullOrWhiteSpace(memberAccount))
            throw new ArgumentException(@"会员账号不能为空", nameof(memberAccount));

        if (string.IsNullOrWhiteSpace(agentName))
            throw new ArgumentException(@"拉手名称不能为空", nameof(agentName));

        if (memberAccount == agentName)
            throw new ArgumentException(@"不能将自己设置为拉手", nameof(agentName));

        if (agentRebatePercent < 0 || agentRebatePercent > 20)
            throw new ArgumentException(@"拉手反水比例必须在0.0到20.0之间", nameof(agentRebatePercent));

        try
        {
            // 直接更新会员的拉手信息（存储拉手名称）
            var affectedRows = await fSql.Update<Member>()
                .Set(m => m.AgentName, agentName.Trim())
                .Set(m => m.AgentRebatePercent, agentRebatePercent)
                .Where(m => m.Account == memberAccount)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            return affectedRows > 0;
        }
        catch (Exception)
        {
            // 记录异常但不抛出，返回false表示设置失败
            return false;
        }
    }

    /// <summary>
    /// 移除会员拉手
    /// </summary>
    /// <param name="memberAccount">会员账号</param>
    /// <returns>是否移除成功</returns>
    /// <exception cref="ArgumentException">当参数无效时抛出</exception>
    public async Task<bool> RemoveMemberAgentAsync(string memberAccount)
    {
        // 参数验证
        if (string.IsNullOrWhiteSpace(memberAccount))
            throw new ArgumentException(@"会员账号不能为空", nameof(memberAccount));

        try
        {
            // 清空会员的拉手信息
            var affectedRows = await fSql.Update<Member>()
                .Set(m => m.AgentName, string.Empty)
                .Set(m => m.AgentRebatePercent, 0m)
                .Where(m => m.Account == memberAccount)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            return affectedRows > 0;
        }
        catch (Exception)
        {
            // 记录异常但不抛出，返回false表示移除失败
            return false;
        }
    }

    /// <summary>
    /// 硬删除会员及其所有关联数据
    /// </summary>
    /// <param name="account">会员账号</param>
    /// <returns>影响的行数</returns>
    /// <exception cref="ArgumentException">当account为空时抛出</exception>
    public async Task<int> DeleteMemberAsync(string account)
    {
        // 参数验证
        if (string.IsNullOrWhiteSpace(account))
            throw new ArgumentException(@"账号不能为空", nameof(account));

        try
        {
            // 使用事务确保数据一致性
            using var transaction = fSql.Ado.TransactionCurrentThread;

            // 1. 删除投注记录
            await fSql.Delete<BetOrder>()
                .Where(b => b.Account == account)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            // 2. 删除财务记录
            await fSql.Delete<FinancialRecord>()
                .Where(f => f.Account == account)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            // 3. 删除上分申请记录
            await fSql.Delete<DepositRequest>()
                .Where(d => d.Account == account)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            // 4. 删除下分申请记录
            await fSql.Delete<WithdrawRequest>()
                .Where(w => w.Account == account)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            // 5. 最后删除会员记录
            var result = await fSql.Delete<Member>()
                .Where(m => m.Account == account)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"硬删除会员及关联数据失败，账号: {Account}", account);
            throw;
        }
    }

    /// <summary>
    /// 根据账号获取会员信息
    /// </summary>
    /// <param name="account">会员账号</param>
    /// <returns>会员信息，如果不存在则返回null</returns>
    /// <exception cref="ArgumentException">当account为空时抛出</exception>
    public async Task<Member?> GetMemberAsync(string account)
    {
        // 参数验证
        if (string.IsNullOrWhiteSpace(account))
            throw new ArgumentException(@"账号不能为空", nameof(account));

        // 使用ToOneAsync避免找不到记录时抛出异常
        return await fSql.Select<Member>()
            .Where(m => m.Account == account)
            .ToOneAsync()
            .ConfigureAwait(false);
    }

    /// <summary>
    /// 获取所有有效会员列表
    /// </summary>
    /// <returns>会员列表</returns>
    public async Task<List<Member>> GetMembersAsync()
    {
        return await fSql.Select<Member>()
            .OrderBy(m => m.Account)
            .ToListAsync()
            .ConfigureAwait(false);
    }

    // 移除GetAllMembersAsync方法，因为改为硬删除机制，GetMembersAsync已经返回所有会员
    // /// <summary>
    // /// 获取所有会员列表（包括已删除的）
    // /// </summary>
    // /// <returns>会员列表</returns>
    // public async Task<List<Member>> GetAllMembersAsync()
    // {
    //     return await fSql.Select<Member>()
    //         .OrderBy(m => m.CreateTime)
    //         .ToListAsync()
    //         .ConfigureAwait(false);
    // }

    /// <summary>
    /// 获取会员信息，如果不存在则自动创建 - 智能会员管理
    ///
    /// 功能：
    /// - 首先尝试查询现有会员
    /// - 如果不存在则自动创建新会员
    /// - 设置默认的会员属性
    /// - 保证返回有效的会员对象
    ///
    /// 业务流程：
    /// 1. 验证账号参数
    /// 2. 查询现有会员
    /// 3. 如果存在则直接返回
    /// 4. 如果不存在则创建新会员
    /// 5. 设置默认值（余额、回水比例等）
    /// 6. 返回会员信息
    ///
    /// 用途：投注、充值等业务中的会员获取
    /// </summary>
    /// <param name="account">会员账号</param>
    /// <param name="nickName">会员昵称（可选，为空时使用账号）</param>
    /// <returns>会员信息（保证不为null）</returns>
    /// <exception cref="ArgumentException">当account为空时抛出</exception>
    public async Task<Member> GetOrCreateMemberAsync(string account, string nickName = "")
    {
        // 参数验证
        if (string.IsNullOrWhiteSpace(account))
            throw new ArgumentException(@"账号不能为空", nameof(account));

        try
        {
            // 首先尝试获取现有会员
            var existingMember = await GetMemberAsync(account).ConfigureAwait(false);
            if (existingMember != null)
            {
                return existingMember;
            }

            // 会员不存在，自动创建新会员
            var defaultRebatePercent = await systemSettingService.GetDefaultMemberRebatePercentAsync();

            var newMember = new Member
            {
                Account = account.Trim(),
                NickName = string.IsNullOrWhiteSpace(nickName) ? account.Trim() : nickName.Trim(),

                Balance = 0,
                RebatePercent = defaultRebatePercent,
                AgentName = string.Empty,
                UserType = EnumUserType.Real // 默认为真人
                // 移除Deleted字段设置，改为硬删除机制
                // 移除CreateTime字段设置，不再需要维护创建时间
            };

            // 直接插入，不进行唯一性检查（因为我们已经确认不存在）
            var result = await fSql.Insert<Member>()
                .AppendData(newMember)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            if (result > 0)
            {
                return newMember;
            }

            throw new InvalidOperationException($@"自动创建会员失败，账号: {account}");
        }
        catch (Exception ex) when (!(ex is ArgumentException || ex is InvalidOperationException))
        {
            throw new InvalidOperationException($@"获取或创建会员时发生异常，账号: {account}", ex);
        }
    }


    // 移除恢复删除会员的方法，因为改为硬删除机制，无法恢复
    // /// <summary>
    // /// 恢复已删除的会员
    // /// </summary>
    // /// <param name="account">会员账号</param>
    // /// <returns>影响的行数</returns>
    // /// <exception cref="ArgumentException">当account为空时抛出</exception>
    // public async Task<int> RestoreMemberAsync(string account)
    // {
    //     // 参数验证
    //     if (string.IsNullOrWhiteSpace(account))
    //         throw new ArgumentException(@"账号不能为空", nameof(account));
    //
    //     return await fSql.Update<Member>()
    //         .Set(m => m.Deleted, false)
    //         .Where(m => m.Account == account && m.Deleted)
    //         .ExecuteAffrowsAsync()
    //         .ConfigureAwait(false);
    // }

    /// <summary>
    /// 根据条件搜索会员
    /// </summary>
    /// <param name="keyword">搜索关键词（账号、姓名、昵称）</param>
    /// <returns>匹配的会员列表</returns>
    public async Task<List<Member>> SearchMembersAsync(string keyword)
    {
        if (string.IsNullOrWhiteSpace(keyword))
            return await GetMembersAsync().ConfigureAwait(false);

        return await fSql.Select<Member>()
            .Where(m => m.Account.Contains(keyword) ||
                        m.NickName.Contains(keyword))
            .OrderBy(m => m.Account)
            .ToListAsync()
            .ConfigureAwait(false);
    }

    #endregion

    #region 私有辅助方法

    /// <summary>
    /// 验证会员信息（用于添加）
    /// </summary>
    /// <param name="member">会员信息</param>
    /// <exception cref="ArgumentNullException">当member为null时抛出</exception>
    /// <exception cref="ArgumentException">当必填字段为空时抛出</exception>
    private static void ValidateMemberForAdd(Member member)
    {
        if (member == null)
            throw new ArgumentNullException(nameof(member), @"会员信息不能为空");

        if (string.IsNullOrWhiteSpace(member.Account))
            throw new ArgumentException(@"账号不能为空", nameof(member));
    }

    /// <summary>
    /// 验证账号唯一性
    /// </summary>
    /// <param name="account">账号</param>
    /// <exception cref="InvalidOperationException">当账号已存在时抛出</exception>
    private async Task ValidateAccountUniquenessAsync(string account)
    {
        var existingMember = await fSql.Select<Member>()
            .Where(m => m.Account == account)
            .ToOneAsync()
            .ConfigureAwait(false);

        if (existingMember != null)
        {
            // 改为硬删除机制，如果账号存在就直接报错
            throw new InvalidOperationException($@"账号 '{account}' 已存在，请使用其他账号");
        }
    }

    /// <summary>
    /// 为新会员设置默认值
    /// </summary>
    /// <param name="member">会员信息</param>
    private async Task SetDefaultValuesForNewMemberAsync(Member member)
    {
        member.UserType = EnumUserType.Real; // 默认为真人
        // 移除Deleted字段设置，改为硬删除机制
        // 移除CreateTime字段设置，不再需要维护创建时间

        // 设置默认返点比例
        if (member.RebatePercent == 0)
        {
            member.RebatePercent = await systemSettingService.GetDefaultMemberRebatePercentAsync();
        }

        // 设置默认余额
        if (member.Balance == 0)
        {
            member.Balance = 0;
        }

        // 移除UnsettledAmount字段设置，改为从投注记录实时计算
        // member.UnsettledAmount = 0;

        // 移除LastIssueProfitLoss字段设置，改为从投注记录实时计算
        // member.LastIssueProfitLoss = 0;

        // 设置默认拉手返点比例
        member.AgentRebatePercent = 0;
    }

    /// <summary>
    /// 获取会员信息，如果不存在则自动创建并获取昵称
    /// </summary>
    /// <param name="account">会员账号</param>
    /// <param name="nickName">会员昵称（可选）</param>
    /// <param name="getNickNameFunc">获取昵称的委托函数（当昵称为空时调用）</param>
    /// <returns>会员信息（保证不为null）</returns>
    /// <exception cref="ArgumentException">当account为空时抛出</exception>
    public async Task<Member> GetOrCreateMemberWithNickNameAsync(string account, string nickName = "", Func<string, Task<string>>? getNickNameFunc = null)
    {
        // 参数验证
        if (string.IsNullOrWhiteSpace(account))
            throw new ArgumentException(@"账号不能为空", nameof(account));

        try
        {
            // 首先尝试获取现有会员
            var existingMember = await GetMemberAsync(account).ConfigureAwait(false);
            if (existingMember != null)
            {
                // 现有用户：检查昵称是否需要更新
                if (string.IsNullOrWhiteSpace(existingMember.NickName) && getNickNameFunc != null)
                {
                    logger.LogInformation(@"现有用户昵称为空，尝试获取昵称，账号: {Account}", account);
                    string fetchedNickName = await getNickNameFunc(account);

                    if (!string.IsNullOrWhiteSpace(fetchedNickName))
                    {
                        await UpdateMemberNickNameAsync(account, fetchedNickName);
                        existingMember.NickName = fetchedNickName; // 更新内存中的昵称
                        logger.LogInformation(@"更新用户昵称成功，账号: {Account}, 昵称: {NickName}", account, fetchedNickName);
                    }
                }
                return existingMember;
            }

            // 会员不存在，自动创建新会员
            string finalNickName = nickName;

            // 如果昵称为空且提供了获取昵称的函数，则尝试获取昵称
            if (string.IsNullOrWhiteSpace(finalNickName) && getNickNameFunc != null)
            {
                logger.LogInformation(@"新用户昵称为空，尝试获取昵称，账号: {Account}", account);
                finalNickName = await getNickNameFunc(account);
            }

            // 如果仍然为空，使用账号作为昵称
            if (string.IsNullOrWhiteSpace(finalNickName))
            {
                finalNickName = account.Trim();
                logger.LogWarning(@"无法获取用户昵称，使用账号作为昵称，账号: {Account}", account);
            }

            var defaultRebatePercent = await systemSettingService.GetDefaultMemberRebatePercentAsync();

            var newMember = new Member
            {
                Account = account.Trim(),
                NickName = finalNickName.Trim(),
                Balance = 0,
                RebatePercent = defaultRebatePercent,
                AgentName = string.Empty,
                UserType = EnumUserType.Real // 默认为真人
            };

            // 直接插入，不进行唯一性检查（因为我们已经确认不存在）
            var result = await fSql.Insert<Member>()
                .AppendData(newMember)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            if (result > 0)
            {
                logger.LogInformation(@"创建新会员成功，账号: {Account}, 昵称: {NickName}", account, finalNickName);
                return newMember;
            }

            throw new InvalidOperationException($@"创建会员失败，账号: {account}");
        }
        catch (Exception ex) when (!(ex is ArgumentException || ex is InvalidOperationException))
        {
            logger.LogError(ex, @"获取或创建会员时发生异常，账号: {Account}", account);
            throw new InvalidOperationException($@"获取或创建会员时发生异常，账号: {account}", ex);
        }
    }

    /// <summary>
    /// 创建会员并自动获取昵称（如果昵称为空）
    /// </summary>
    /// <param name="account">会员账号</param>
    /// <param name="nickName">会员昵称（可选，为空时会自动获取）</param>
    /// <returns>新创建的会员信息</returns>
    /// <exception cref="ArgumentException">当account为空时抛出</exception>
    /// <exception cref="InvalidOperationException">当账号已存在或创建失败时抛出</exception>
    public async Task<Member> CreateMemberWithAutoNickNameAsync(string account, string nickName = "")
    {
        // 参数验证
        if (string.IsNullOrWhiteSpace(account))
            throw new ArgumentException(@"账号不能为空", nameof(account));

        try
        {
            // 检查账号是否已存在
            var existingMember = await GetMemberAsync(account).ConfigureAwait(false);
            if (existingMember != null)
            {
                throw new InvalidOperationException($@"账号 '{account}' 已存在，无法重复创建");
            }

            // 如果昵称为空，使用账号作为昵称（实际应该调用聊天平台接口获取）
            string finalNickName = string.IsNullOrWhiteSpace(nickName) ? account.Trim() : nickName.Trim();

            // 获取默认返点比例
            var defaultRebatePercent = await systemSettingService.GetDefaultMemberRebatePercentAsync();

            // 创建新会员
            var newMember = new Member
            {
                Account = account.Trim(),
                NickName = finalNickName,

                Balance = 0,
                RebatePercent = defaultRebatePercent,
                AgentName = string.Empty,
                UserType = EnumUserType.Real // 默认为真人
                // 移除Deleted字段设置，改为硬删除机制
                // 移除CreateTime字段设置，不再需要维护创建时间
            };

            // 插入数据库
            var result = await fSql.Insert<Member>()
                .AppendData(newMember)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            if (result > 0)
            {
                return newMember;
            }

            throw new InvalidOperationException($@"创建会员失败，账号: {account}");
        }
        catch (Exception ex) when (!(ex is ArgumentException || ex is InvalidOperationException))
        {
            throw new InvalidOperationException($@"创建会员时发生异常，账号: {account}", ex);
        }
    }

    #endregion
}