{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"LicenseGenerator/1.0.0": {"dependencies": {"CommandGuard.Licensing": "1.0.0"}, "runtime": {"LicenseGenerator.dll": {}}}, "System.CodeDom/8.0.0": {}, "System.Management/8.0.0": {"dependencies": {"System.CodeDom": "8.0.0"}, "runtime": {"lib/net8.0/System.Management.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "CommandGuard.Licensing/1.0.0": {"dependencies": {"System.Management": "8.0.0"}, "runtime": {"CommandGuard.Licensing.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"LicenseGenerator/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "System.CodeDom/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WTlRjL6KWIMr/pAaq3rYqh0TJlzpouaQ/W1eelssHgtlwHAH25jXTkUphTYx9HaIIf7XA6qs/0+YhtLEQRkJ+Q==", "path": "system.codedom/8.0.0", "hashPath": "system.codedom.8.0.0.nupkg.sha512"}, "System.Management/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jrK22i5LRzxZCfGb+tGmke2VH7oE0DvcDlJ1HAKYU8cPmD8XnpUT0bYn2Gy98GEhGjtfbR/sxKTVb+dE770pfA==", "path": "system.management/8.0.0", "hashPath": "system.management.8.0.0.nupkg.sha512"}, "CommandGuard.Licensing/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}