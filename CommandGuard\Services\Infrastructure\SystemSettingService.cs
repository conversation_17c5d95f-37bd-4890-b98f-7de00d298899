using System.ComponentModel;
using System.Globalization;
using CommandGuard.Interfaces.Infrastructure;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Infrastructure;

/// <summary>
/// 系统设置数据库服务实现
/// </summary>
public class SystemSettingService(IFreeSql fSql, ILogger<SystemSettingService> logger) : ISystemSettingService
{
    /// <summary>
    /// 获取所有系统设置
    /// </summary>
    public async Task<List<SystemSetting>> GetAllSettingsAsync()
    {
        try
        {
            return await fSql.Select<SystemSetting>()
                .OrderBy(x => x.SortOrder)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取所有系统设置失败");
            return [];
        }
    }

    /// <summary>
    /// 根据设置键获取系统设置
    /// </summary>
    public async Task<SystemSetting?> GetSettingByKeyAsync(string settingKey)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(settingKey))
                return null;

            return await fSql.Select<SystemSetting>()
                .Where(x => x.SettingKey == settingKey.Trim())
                .FirstAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"根据设置键获取系统设置失败: {SettingKey}", settingKey);
            return null;
        }
    }

    /// <summary>
    /// 获取设置值 - 泛型类型安全的设置获取
    ///
    /// 功能：
    /// - 根据设置键获取对应的值
    /// - 自动进行类型转换
    /// - 支持默认值机制
    /// - 类型安全的值获取
    ///
    /// 支持的类型：
    /// - bool: 布尔值设置
    /// - int: 整数设置
    /// - decimal: 小数设置
    /// - string: 字符串设置
    ///
    /// 异常处理：
    /// - 设置不存在时返回默认值
    /// - 类型转换失败时返回默认值
    /// - 数据库异常时返回默认值
    /// </summary>
    public async Task<T> GetSettingValueAsync<T>(string settingKey, T defaultValue = default!)
    {
        try
        {
            var setting = await GetSettingByKeyAsync(settingKey);
            if (setting == null)
            {
                return defaultValue;
            }

            return ConvertValue(setting.SettingValue, defaultValue);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取设置值失败: {SettingKey}", settingKey);
            return defaultValue;
        }
    }

    /// <summary>
    /// 设置值 - 泛型类型安全的设置更新
    ///
    /// 功能：
    /// - 根据设置键更新对应的值
    /// - 自动进行类型转换和验证
    /// - 支持新增和更新操作
    /// - 类型安全的值设置
    ///
    /// 操作逻辑：
    /// 1. 检查设置是否已存在
    /// 2. 如果存在则更新值
    /// 3. 如果不存在则创建新设置
    /// 4. 自动设置类型信息
    ///
    /// 支持的类型：
    /// - bool: 布尔值设置
    /// - int: 整数设置
    /// - decimal: 小数设置
    /// - string: 字符串设置
    /// </summary>
    public async Task<bool> SetSettingValueAsync<T>(string settingKey, T value)
    {
        try
        {
            var existingSetting = await GetSettingByKeyAsync(settingKey);
            var settingValue = ConvertToString(value);
            var settingType = GetSettingType<T>();

            if (existingSetting != null)
            {
                // 更新现有设置
                existingSetting.SettingValue = settingValue;
                existingSetting.SettingType = settingType;

                return await UpdateSettingAsync(existingSetting);
            }

            // 创建新设置
            var newSetting = new SystemSetting
            {
                SettingKey = settingKey,
                SettingValue = settingValue,
                SettingType = settingType,
                DefaultValue = settingValue,
                SortOrder = 1 // 新创建的设置默认排在最后
            };

            return await CreateSettingAsync(newSetting);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"设置值失败: {SettingKey} = {Value}", settingKey, value);
            return false;
        }
    }

    /// <summary>
    /// 创建系统设置
    /// </summary>
    public async Task<bool> CreateSettingAsync(SystemSetting systemSetting)
    {
        try
        {
            var result = await fSql.Insert<SystemSetting>()
                .AppendData(systemSetting)
                .ExecuteAffrowsAsync();

            if (result > 0)
            {
                logger.LogInformation(@"创建系统设置成功: {SettingKey}", systemSetting.SettingKey);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"创建系统设置失败: {SettingKey}", systemSetting.SettingKey);
            return false;
        }
    }

    /// <summary>
    /// 更新系统设置
    /// </summary>
    public async Task<bool> UpdateSettingAsync(SystemSetting systemSetting)
    {
        try
        {
            var result = await fSql.Update<SystemSetting>()
                .SetSource(systemSetting)
                .ExecuteAffrowsAsync();

            if (result > 0)
            {
                logger.LogInformation(@"更新系统设置成功: {SettingKey}", systemSetting.SettingKey);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"更新系统设置失败: {SettingKey}", systemSetting.SettingKey);
            return false;
        }
    }

    /// <summary>
    /// 删除系统设置
    /// </summary>
    public async Task<bool> DeleteSettingAsync(int id)
    {
        try
        {
            var result = await fSql.Delete<SystemSetting>()
                .Where(x => x.Id == id)
                .ExecuteAffrowsAsync();

            if (result > 0)
            {
                logger.LogInformation(@"删除系统设置成功: ID={Id}", id);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"删除系统设置失败: ID={Id}", id);
            return false;
        }
    }

    /// <summary>
    /// 批量更新系统设置
    /// </summary>
    public async Task<bool> BatchUpdateSettingsAsync(List<SystemSetting> systemSettings)
    {
        try
        {
            var result = await fSql.Update<SystemSetting>()
                .SetSource(systemSettings)
                .ExecuteAffrowsAsync();

            if (result > 0)
            {
                logger.LogInformation(@"批量更新系统设置成功: {Count} 条", result);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"批量更新系统设置失败");
            return false;
        }
    }

    /// <summary>
    /// 检查设置是否存在
    /// </summary>
    public async Task<bool> ExistsAsync(string settingKey)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(settingKey))
                return false;

            var count = await fSql.Select<SystemSetting>()
                .Where(x => x.SettingKey == settingKey.Trim())
                .CountAsync();

            return count > 0;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"检查系统设置是否存在失败: {SettingKey}", settingKey);
            return false;
        }
    }

    /// <summary>
    /// 初始化默认系统设置 - 系统首次启动的配置初始化
    ///
    /// 功能：
    /// - 检查数据库中是否已有系统设置
    /// - 如果没有则创建默认的系统设置
    /// - 包含所有必要的系统配置项
    ///
    /// 默认设置项：
    /// - 发送6路图: false
    /// - 发送7路图: false
    /// - 开启对冲: false
    /// - 假人自动上分: false
    /// - 返点比例: 1.5%
    ///
    /// 使用场景：
    /// - 系统初始化
    /// - 出厂设置恢复
    /// - 数据库重置后的配置恢复
    /// </summary>
    public async Task<bool> InitializeDefaultSettingsAsync()
    {
        try
        {
            logger.LogInformation(@"开始初始化默认系统设置");

            // 获取所有默认配置项
            var defaultSettings = GetDefaultSettingsList();

            // 获取现有配置项
            var existingSettings = await fSql.Select<SystemSetting>().ToListAsync();
            var existingKeys = existingSettings.Select(s => s.SettingKey).ToHashSet();

            // 找出缺失的配置项
            var missingSettings = defaultSettings.Where(ds => !existingKeys.Contains(ds.SettingKey)).ToList();

            if (missingSettings.Count == 0)
            {
                logger.LogInformation(@"所有系统设置已存在，无需添加新配置项");
                return true;
            }

            // 添加缺失的配置项
            logger.LogInformation(@"发现 {Count} 个缺失的配置项，开始添加", missingSettings.Count);

            var result = await fSql.Insert<SystemSetting>()
                .AppendData(missingSettings)
                .ExecuteAffrowsAsync();

            if (result > 0)
            {
                logger.LogInformation(@"成功添加 {Count} 个系统设置", result);
                return true;
            }

            logger.LogWarning(@"添加系统设置失败");
            return false;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"初始化默认系统设置失败");
            return false;
        }
    }

    /// <summary>
    /// 强制重置系统设置为出厂默认值 - 出厂设置专用
    ///
    /// 功能：
    /// - 强制清除所有现有系统设置
    /// - 重新创建默认系统设置
    /// - 不检查现有设置，直接重置
    ///
    /// 与 InitializeDefaultSettingsAsync 的区别：
    /// - InitializeDefaultSettingsAsync: 只添加缺失的设置项
    /// - ResetToFactoryDefaultSettingsAsync: 强制清除并重新创建所有设置
    ///
    /// 使用场景：
    /// - 出厂设置重置
    /// - 强制恢复默认配置
    /// </summary>
    public async Task<bool> ResetToFactoryDefaultSettingsAsync()
    {
        try
        {
            logger.LogInformation(@"开始强制重置系统设置为出厂默认值");

            // 1. 强制清除所有现有系统设置
            var deletedCount = await fSql.Delete<SystemSetting>().Where("1=1").ExecuteAffrowsAsync();
            logger.LogInformation(@"已清除现有系统设置: {Count} 条", deletedCount);

            // 2. 创建默认系统设置
            var defaultSettings = GetDefaultSettingsList();
            var insertedCount = await fSql.Insert<SystemSetting>()
                .AppendData(defaultSettings)
                .ExecuteAffrowsAsync();

            logger.LogInformation(@"已创建默认系统设置: {Count} 条", insertedCount);

            var success = insertedCount > 0;
            if (success)
            {
                logger.LogInformation(@"强制重置系统设置为出厂默认值成功");
            }
            else
            {
                logger.LogWarning(@"强制重置系统设置为出厂默认值失败：未能创建默认设置");
            }

            return success;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"强制重置系统设置为出厂默认值失败");
            return false;
        }
    }

    #region 私有辅助方法

    /// <summary>
    /// 转换值为指定类型
    /// </summary>
    private static T ConvertValue<T>(string value, T defaultValue)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(value))
                return defaultValue;

            var targetType = typeof(T);
            var underlyingType = Nullable.GetUnderlyingType(targetType) ?? targetType;

            if (underlyingType == typeof(bool))
            {
                // 处理布尔值的多种表示方式
                var lowerValue = value.ToLowerInvariant();
                if (lowerValue is "true" or "1" or "yes" or "on")
                    return (T)(object)true;
                if (lowerValue is "false" or "0" or "no" or "off")
                    return (T)(object)false;
            }

            var converter = TypeDescriptor.GetConverter(underlyingType);
            if (converter.CanConvertFrom(typeof(string)))
            {
                var convertedValue = converter.ConvertFromString(null, CultureInfo.InvariantCulture, value);
                return (T)convertedValue!;
            }

            return defaultValue;
        }
        catch
        {
            return defaultValue;
        }
    }

    /// <summary>
    /// 转换值为字符串
    /// </summary>
    private static string ConvertToString<T>(T value)
    {
        if (value == null)
            return string.Empty;

        if (value is bool boolValue)
            return boolValue.ToString();

        return value.ToString() ?? string.Empty;
    }

    /// <summary>
    /// 获取设置类型
    /// </summary>
    private static string GetSettingType<T>()
    {
        var type = typeof(T);
        var underlyingType = Nullable.GetUnderlyingType(type) ?? type;

        if (underlyingType == typeof(bool))
            return SystemSettingTypes.Boolean;
        if (underlyingType == typeof(int) || underlyingType == typeof(long))
            return SystemSettingTypes.Integer;
        if (underlyingType == typeof(decimal) || underlyingType == typeof(double) || underlyingType == typeof(float))
            return SystemSettingTypes.Decimal;

        return SystemSettingTypes.String;
    }

    /// <summary>
    /// 获取默认设置列表
    /// </summary>
    private static List<SystemSetting> GetDefaultSettingsList()
    {
        return
        [
            new() { SettingKey = @"开启对冲", SettingValue = @"False", SettingType = SystemSettingTypes.Boolean, DefaultValue = @"False", SortOrder = 10 },
            new() { SettingKey = @"发送6路图", SettingValue = @"True", SettingType = SystemSettingTypes.Boolean, DefaultValue = @"True", SortOrder = 20 },
            new() { SettingKey = @"发送7路图", SettingValue = @"True", SettingType = SystemSettingTypes.Boolean, DefaultValue = @"True", SortOrder = 30 },
            new() { SettingKey = @"假人自动上分", SettingValue = @"False", SettingType = SystemSettingTypes.Boolean, DefaultValue = @"False", SortOrder = 40 },
            new() { SettingKey = @"自动回水", SettingValue = @"False", SettingType = SystemSettingTypes.Boolean, DefaultValue = @"False", SortOrder = 50 },
            new() { SettingKey = @"开启图片背景", SettingValue = @"True", SettingType = SystemSettingTypes.Boolean, DefaultValue = @"True", SortOrder = 60 },
            new() { SettingKey = @"摊路图类型", SettingValue = @"1", SettingType = SystemSettingTypes.Integer, DefaultValue = @"1", SortOrder = 70 },
            new() { SettingKey = @"返点比例", SettingValue = @"1.5", SettingType = SystemSettingTypes.Decimal, DefaultValue = @"1.5", SortOrder = 80 },
            new() { SettingKey = @"单期限额", SettingValue = @"100000", SettingType = SystemSettingTypes.Decimal, DefaultValue = @"100000", SortOrder = 90 }
        ];
    }

    #endregion

    #region 业务配置便捷方法

    /// <summary>
    /// 获取会员默认返点比例
    /// </summary>
    /// <returns>会员默认返点比例</returns>
    public async Task<decimal> GetDefaultMemberRebatePercentAsync()
    {
        return await GetSettingValueAsync("返点比例", 1.5m);
    }

    /// <summary>
    /// 更新会员返点比例
    /// </summary>
    /// <param name="rebatePercent">返点比例</param>
    /// <returns>是否更新成功</returns>
    public async Task<bool> UpdateMemberRebatePercentAsync(decimal rebatePercent)
    {
        return await SetSettingValueAsync("返点比例", rebatePercent);
    }

    /// <summary>
    /// 获取单期限额
    /// </summary>
    /// <returns>单期限额</returns>
    public async Task<decimal> GetSingleIssueLimitAsync()
    {
        return await GetSettingValueAsync("单期限额", 100000m);
    }

    /// <summary>
    /// 更新单期限额
    /// </summary>
    /// <param name="limit">单期限额</param>
    /// <returns>是否更新成功</returns>
    public async Task<bool> UpdateSingleIssueLimitAsync(decimal limit)
    {
        return await SetSettingValueAsync("单期限额", limit);
    }

    #endregion
}