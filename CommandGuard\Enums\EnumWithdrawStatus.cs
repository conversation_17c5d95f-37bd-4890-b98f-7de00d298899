namespace CommandGuard.Enums;

/// <summary>
/// 下分申请状态枚举
/// </summary>
public enum EnumWithdrawStatus
{
    /// <summary>
    /// 待审核（已冻结金额）
    /// </summary>
    Pending = 0,

    /// <summary>
    /// 已批准
    /// </summary>
    Approved = 1,

    /// <summary>
    /// 已拒绝（已退还冻结金额）
    /// </summary>
    Rejected = 2,

    /// <summary>
    /// 余额不足，自动拒绝
    /// </summary>
    InsufficientBalance = 3
}
