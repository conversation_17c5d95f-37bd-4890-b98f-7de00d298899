﻿using System.ComponentModel.DataAnnotations;
using FreeSql.DataAnnotations;
using CommandGuard.Enums;

namespace CommandGuard.Models;

/// <summary>
/// 会员实体类
/// 包含会员的基本信息、财务信息和状态信息
/// 支持软删除和层级关系管理
/// </summary>
[Table(Name = "Members")]
[Index("idx_account", "Account", true)] // 账号唯一索引
[Index("idx_agent_name", "AgentName")] // 拉手名称索引
public class Member
{
    /// <summary>
    /// 主键ID，自增长
    /// </summary>
    [Column(IsIdentity = true, IsPrimary = true)]
    public int Id { get; set; }

    /// <summary>
    /// 会员账号，唯一标识
    /// </summary>
    [Required(ErrorMessage = @"账号不能为空")]
    [StringLength(50, MinimumLength = 3, ErrorMessage = @"账号长度必须在3-50个字符之间")]
    [RegularExpression(@"^[a-zA-Z0-9_]+$", ErrorMessage = @"账号只能包含字母、数字和下划线")]
    [Column(StringLength = 50)]
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 会员昵称
    /// </summary>
    [StringLength(200, ErrorMessage = @"昵称长度不能超过200个字符")]
    [Column(StringLength = 200)]
    public string NickName { get; set; } = string.Empty;

    /// <summary>
    /// 账户余额（可用积分）
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = @"余额不能为负数")]
    [Column(Precision = 18, Scale = 2)]
    public decimal Balance { get; set; }

    /// <summary>
    /// 返点百分比（0-100）
    /// 用于计算给自己的返点金额
    /// </summary>
    [Range(0, 100, ErrorMessage = @"返点百分比必须在0-100之间")]
    [Column(Precision = 5, Scale = 2)]
    public decimal RebatePercent { get; set; }

    /// <summary>
    /// 拉手名称
    /// </summary>
    [StringLength(50, ErrorMessage = @"拉手名称长度不能超过50个字符")]
    [Column(StringLength = 50)]
    public string AgentName { get; set; } = string.Empty;

    /// <summary>
    /// 给上级拉手返点比例（0-100）
    /// 用于计算给拉手上级的返点金额
    /// </summary>
    [Range(0, 100, ErrorMessage = @"给上级拉手返点比例必须在0-100之间")]
    [Column(Precision = 5, Scale = 2)]
    public decimal AgentRebatePercent { get; set; }

    /// <summary>
    /// 用户类型（真人/假人）
    /// </summary>
    public EnumUserType UserType { get; set; } = EnumUserType.Real;
}