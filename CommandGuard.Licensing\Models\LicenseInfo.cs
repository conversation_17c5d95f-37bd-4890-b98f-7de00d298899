using System.Text.Json.Serialization;

namespace CommandGuard.Licensing.Models;

/// <summary>
/// 授权信息模型
/// 包含机器码、授权时间、到期时间等信息
/// </summary>
public class LicenseInfo
{
    /// <summary>
    /// 机器码（硬件指纹）
    /// 基于CPU、主板、硬盘等硬件信息生成的唯一标识
    /// </summary>
    [JsonPropertyName("machineCode")]
    public string MachineCode { get; set; } = string.Empty;

    /// <summary>
    /// 授权用户名/公司名
    /// </summary>
    [JsonPropertyName("licensedTo")]
    public string LicensedTo { get; set; } = string.Empty;

    /// <summary>
    /// 产品名称
    /// </summary>
    [JsonPropertyName("productName")]
    public string ProductName { get; set; } = "指令卫士";

    /// <summary>
    /// 产品版本
    /// </summary>
    [JsonPropertyName("productVersion")]
    public string ProductVersion { get; set; } = "1.0.0";

    /// <summary>
    /// 授权生成时间
    /// </summary>
    [JsonPropertyName("issuedAt")]
    public DateTime IssuedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 授权到期时间
    /// </summary>
    [JsonPropertyName("expiresAt")]
    public DateTime ExpiresAt { get; set; } = DateTime.Now.AddYears(1);

    /// <summary>
    /// 授权类型
    /// Trial: 试用版, Standard: 标准版, Professional: 专业版, Enterprise: 企业版
    /// </summary>
    [JsonPropertyName("licenseType")]
    public LicenseType LicenseType { get; set; } = LicenseType.Trial;

    /// <summary>
    /// 最大并发用户数（-1表示无限制）
    /// </summary>
    [JsonPropertyName("maxUsers")]
    public int MaxUsers { get; set; } = 1;

    /// <summary>
    /// 功能特性列表
    /// </summary>
    [JsonPropertyName("features")]
    public List<string> Features { get; set; } = new();

    /// <summary>
    /// 是否为永久授权
    /// </summary>
    [JsonPropertyName("isPermanent")]
    public bool IsPermanent { get; set; } = false;

    /// <summary>
    /// 授权序列号
    /// </summary>
    [JsonPropertyName("serialNumber")]
    public string SerialNumber { get; set; } = string.Empty;

    /// <summary>
    /// 数字签名
    /// 用于验证授权文件的完整性和真实性
    /// </summary>
    [JsonPropertyName("signature")]
    public string Signature { get; set; } = string.Empty;

    /// <summary>
    /// 检查授权是否有效
    /// </summary>
    /// <returns>是否有效</returns>
    public bool IsValid()
    {
        // 检查是否过期
        if (!IsPermanent && DateTime.Now > ExpiresAt)
        {
            return false;
        }

        // 检查必要字段
        if (string.IsNullOrEmpty(MachineCode) || 
            string.IsNullOrEmpty(LicensedTo) || 
            string.IsNullOrEmpty(SerialNumber))
        {
            return false;
        }

        return true;
    }

    /// <summary>
    /// 获取剩余天数
    /// </summary>
    /// <returns>剩余天数，-1表示永久</returns>
    public int GetRemainingDays()
    {
        if (IsPermanent)
        {
            return -1;
        }

        var remaining = ExpiresAt - DateTime.Now;
        return Math.Max(0, (int)remaining.TotalDays);
    }
}

/// <summary>
/// 授权类型枚举
/// </summary>
public enum LicenseType
{
    /// <summary>
    /// 试用版
    /// </summary>
    Trial = 0,

    /// <summary>
    /// 标准版
    /// </summary>
    Standard = 1,

    /// <summary>
    /// 专业版
    /// </summary>
    Professional = 2,

    /// <summary>
    /// 企业版
    /// </summary>
    Enterprise = 3
}
