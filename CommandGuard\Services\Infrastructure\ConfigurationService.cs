using CommandGuard.Enums;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace CommandGuard.Services.Infrastructure;

/// <summary>
/// 配置管理服务 - 负责应用程序配置的持久化存储
/// 
/// 功能说明：
/// - 保存和读取用户配置
/// - 支持聊天平台选择记忆
/// - 配置文件的安全读写
/// - 配置验证和默认值处理
/// 
/// 设计目标：
/// - 提供简单易用的配置API
/// - 确保配置文件的安全性
/// - 支持配置的版本管理
/// - 便于扩展新的配置项
/// </summary>
public class ConfigurationService : IConfigurationService
{
    #region 私有字段

    private readonly ILogger<ConfigurationService> _logger;
    private readonly string _configFilePath;
    private UserConfiguration? _cachedConfig;

    #endregion

    #region 构造函数

    /// <summary>
    /// 配置管理服务构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public ConfigurationService(ILogger<ConfigurationService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // 配置文件保存在程序目录下的Config文件夹中
        var configDirectory = Path.Combine(Application.StartupPath, "Config");
        _configFilePath = Path.Combine(configDirectory, "config.json");

        _logger.LogDebug(@"配置服务已初始化，配置文件路径: {ConfigPath}", _configFilePath);
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 获取用户配置
    /// </summary>
    /// <returns>用户配置对象</returns>
    public async Task<UserConfiguration> GetConfigurationAsync()
    {
        try
        {
            // 如果有缓存配置，直接返回
            if (_cachedConfig != null)
            {
                return _cachedConfig;
            }

            // 尝试从文件读取配置
            if (File.Exists(_configFilePath))
            {
                var jsonContent = await File.ReadAllTextAsync(_configFilePath);
                if (!string.IsNullOrWhiteSpace(jsonContent))
                {
                    var options = new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                        Converters = { new JsonStringEnumConverter() }
                    };

                    var config = JsonSerializer.Deserialize<UserConfiguration>(jsonContent, options);
                    if (config != null)
                    {
                        _cachedConfig = config;
                        _logger.LogInformation(@"成功从文件加载用户配置，平台: {Platform}", config.SelectedChatApp);
                        return config;
                    }
                }
            }

            // 如果文件不存在或读取失败，返回默认配置
            _cachedConfig = GetDefaultConfiguration();
            _logger.LogInformation(@"使用默认配置");
            return _cachedConfig;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"读取用户配置失败，使用默认配置");
            _cachedConfig = GetDefaultConfiguration();
            return _cachedConfig;
        }
    }

    /// <summary>
    /// 保存用户配置
    /// </summary>
    /// <param name="configuration">要保存的配置</param>
    public async Task SaveConfigurationAsync(UserConfiguration configuration)
    {
        try
        {
            if (configuration == null)
            {
                throw new ArgumentNullException(nameof(configuration));
            }

            // 更新配置时间戳
            configuration.LastUpdated = DateTime.Now;

            // 序列化配置为JSON
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                Converters = { new JsonStringEnumConverter() } // 使用字符串形式序列化枚举
            };

            var jsonContent = JsonSerializer.Serialize(configuration, options);

            // 确保Config目录存在
            var directory = Path.GetDirectoryName(_configFilePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
                _logger.LogInformation(@"已创建配置目录: {Directory}", directory);
            }

            // 写入文件
            await File.WriteAllTextAsync(_configFilePath, jsonContent);

            // 更新缓存
            _cachedConfig = configuration;

            _logger.LogInformation(@"用户配置已保存到文件: {ConfigPath}", _configFilePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"保存用户配置失败");
            throw;
        }
    }

    /// <summary>
    /// 获取聊天平台配置
    /// </summary>
    /// <returns>当前选择的聊天平台</returns>
    public async Task<EnumChatApp> GetSelectedChatAppAsync()
    {
        try
        {
            var config = await GetConfigurationAsync();
            return config.SelectedChatApp;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"获取聊天平台配置失败，使用默认值");
            return EnumChatApp.一起聊吧;
        }
    }

    /// <summary>
    /// 保存聊天平台配置
    /// </summary>
    /// <param name="chatApp">选择的聊天平台</param>
    public async Task SaveSelectedChatAppAsync(EnumChatApp chatApp)
    {
        try
        {
            var config = await GetConfigurationAsync();
            config.SelectedChatApp = chatApp;
            await SaveConfigurationAsync(config);
            
            _logger.LogInformation(@"聊天平台配置已保存: {ChatApp}", chatApp);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"保存聊天平台配置失败");
            throw;
        }
    }

    /// <summary>
    /// 清除配置缓存
    /// </summary>
    public void ClearCache()
    {
        _cachedConfig = null;
        _logger.LogDebug(@"配置缓存已清除");
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 获取默认配置
    /// </summary>
    /// <returns>默认用户配置</returns>
    private static UserConfiguration GetDefaultConfiguration()
    {
        return new UserConfiguration
        {
            SelectedChatApp = EnumChatApp.一起聊吧,
            Version = "1.0.0",
            CreatedAt = DateTime.Now,
            LastUpdated = DateTime.Now
        };
    }

    #endregion
}

/// <summary>
/// 配置管理服务接口
/// </summary>
public interface IConfigurationService
{
    /// <summary>
    /// 获取用户配置
    /// </summary>
    Task<UserConfiguration> GetConfigurationAsync();

    /// <summary>
    /// 保存用户配置
    /// </summary>
    Task SaveConfigurationAsync(UserConfiguration configuration);

    /// <summary>
    /// 获取聊天平台配置
    /// </summary>
    Task<EnumChatApp> GetSelectedChatAppAsync();

    /// <summary>
    /// 保存聊天平台配置
    /// </summary>
    Task SaveSelectedChatAppAsync(EnumChatApp chatApp);

    /// <summary>
    /// 清除配置缓存
    /// </summary>
    void ClearCache();
}

/// <summary>
/// 用户配置数据模型
/// </summary>
public class UserConfiguration
{
    /// <summary>
    /// 选择的聊天平台
    /// </summary>
    public EnumChatApp SelectedChatApp { get; set; } = EnumChatApp.一起聊吧;

    /// <summary>
    /// 配置版本
    /// </summary>
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// 配置创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.Now;

    /// <summary>
    /// 窗口位置和大小配置（预留扩展）
    /// </summary>
    public WindowConfiguration? WindowConfig { get; set; }

    /// <summary>
    /// 其他用户偏好设置（预留扩展）
    /// </summary>
    public Dictionary<string, object>? UserPreferences { get; set; }
}

/// <summary>
/// 窗口配置数据模型（预留扩展）
/// </summary>
public class WindowConfiguration
{
    public int X { get; set; }
    public int Y { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
    public bool IsMaximized { get; set; }
}
