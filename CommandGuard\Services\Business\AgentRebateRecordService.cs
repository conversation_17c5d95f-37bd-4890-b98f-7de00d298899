using CommandGuard.Enums;
using CommandGuard.Interfaces.Business;
using CommandGuard.Models;
using CommandGuard.ViewModels;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Business;

/// <summary>
/// 拉手返点记录服务实现
/// 提供拉手返点记录的查询、统计等功能
/// </summary>
public class AgentRebateRecordService(
    ILogger<AgentRebateRecordService> logger,
    IFreeSql fSql) : IAgentRebateRecordService
{
    /// <summary>
    /// 根据条件查询拉手返点记录
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="agentName">拉手名称（可选）</param>
    /// <returns>拉手返点记录列表</returns>
    public async Task<List<AgentRebateRecordViewModel>> QueryAgentRebateRecordsAsync(
        DateTime startTime,
        DateTime endTime,
        string? agentName = null)
    {
        try
        {
            logger.LogInformation(@"开始查询拉手返点记录，时间范围: {StartTime} - {EndTime}, 拉手名称: {AgentName}",
                startTime, endTime, agentName ?? "全部");

            // 查询所有有拉手的会员
            var membersQuery = fSql.Select<Member>()
                .Where(m => !string.IsNullOrEmpty(m.AgentName));

            // 如果指定了拉手名称，则按拉手名称过滤
            if (!string.IsNullOrWhiteSpace(agentName))
            {
                membersQuery = membersQuery.Where(m => m.AgentName == agentName.Trim());
            }

            var membersWithAgent = await membersQuery.ToListAsync();

            if (!membersWithAgent.Any())
            {
                logger.LogInformation(@"未找到符合条件的拉手会员");
                return [];
            }

            // 按拉手名称分组
            var agentGroups = membersWithAgent.GroupBy(m => m.AgentName).ToList();
            var agentRebateRecords = new List<AgentRebateRecordViewModel>();

            foreach (var agentGroup in agentGroups)
            {
                var currentAgentName = agentGroup.Key;
                var agentMembers = agentGroup.ToList();

                // 为每个会员单独计算返点记录
                foreach (var member in agentMembers)
                {
                    // 查询该会员在指定时间范围内的有效投注记录（排除和局）
                    var memberValidBetOrders = await fSql.Select<BetOrder>()
                        .Where(bo => bo.Account == member.Account &&
                                    bo.CreatedTime >= startTime &&
                                    bo.CreatedTime <= endTime &&
                                    bo.Status != EnumBetOrderStatus.Draw && // 排除和局
                                    (bo.Status == EnumBetOrderStatus.Win || bo.Status == EnumBetOrderStatus.Lose)) // 只包含输赢
                        .ToListAsync();

                    // 计算该会员的有效流水
                    var memberValidTurnover = memberValidBetOrders.Sum(bo => bo.Amount);

                    // 创建该会员的拉手返点记录视图模型
                    var agentRebateRecord = new AgentRebateRecordViewModel
                    {
                        // 显示会员的昵称
                        NickName = member.NickName,
                        // 显示会员的账号
                        Account = member.Account,
                        // 该会员的有效流水
                        ValidTurnover = memberValidTurnover,
                        // 该会员设置的拉手返点比例
                        AgentRebatePercent = member.AgentRebatePercent
                    };

                    agentRebateRecords.Add(agentRebateRecord);

                    logger.LogDebug(@"会员 {Account}({NickName}) - 拉手: {AgentName}, 有效流水: {ValidTurnover:F2}, 拉手返点比例: {RebatePercent:F2}%",
                        member.Account, member.NickName, currentAgentName, memberValidTurnover, member.AgentRebatePercent);
                }
            }

            // 按有效流水降序排序
            agentRebateRecords = agentRebateRecords.OrderByDescending(r => r.ValidTurnover).ToList();

            logger.LogInformation(@"拉手返点记录查询完成，共找到 {Count} 个拉手", agentRebateRecords.Count);
            return agentRebateRecords;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"查询拉手返点记录时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 计算总有效流水
    /// </summary>
    /// <param name="records">拉手返点记录列表</param>
    /// <returns>总有效流水</returns>
    public decimal CalculateTotalValidTurnover(List<AgentRebateRecordViewModel> records)
    {
        return records?.Sum(r => r.ValidTurnover) ?? 0;
    }

    /// <summary>
    /// 计算总返水金额
    /// </summary>
    /// <param name="records">拉手返点记录列表</param>
    /// <returns>总返水金额</returns>
    public decimal CalculateTotalRebateAmount(List<AgentRebateRecordViewModel> records)
    {
        return records?.Sum(r => r.RebateAmount) ?? 0;
    }

    /// <summary>
    /// 获取活跃会员数量
    /// </summary>
    /// <param name="records">拉手返点记录列表</param>
    /// <returns>活跃会员数量（有流水的会员）</returns>
    public int GetActiveAgentCount(List<AgentRebateRecordViewModel> records)
    {
        return records?.Count(r => r.ValidTurnover > 0) ?? 0;
    }

    /// <summary>
    /// 获取会员总数
    /// </summary>
    /// <param name="records">拉手返点记录列表</param>
    /// <returns>会员总数</returns>
    public int GetTotalAgentCount(List<AgentRebateRecordViewModel> records)
    {
        return records?.Count ?? 0;
    }

    /// <summary>
    /// 获取拉手返点记录统计信息
    /// </summary>
    /// <param name="records">拉手返点记录列表</param>
    /// <returns>统计信息元组（总流水，总返水，活跃会员数，会员总数）</returns>
    public (decimal TotalValidTurnover, decimal TotalRebateAmount, int ActiveAgentCount, int TotalAgentCount)
        GetAgentRebateStatistics(List<AgentRebateRecordViewModel> records)
    {
        if (records == null || !records.Any())
        {
            return (0, 0, 0, 0);
        }

        var totalValidTurnover = CalculateTotalValidTurnover(records);
        var totalRebateAmount = CalculateTotalRebateAmount(records);
        var activeAgentCount = GetActiveAgentCount(records);
        var totalAgentCount = GetTotalAgentCount(records);

        logger.LogDebug(@"拉手返点统计 - 总流水: {TotalValidTurnover:F2}, 总返水: {TotalRebateAmount:F2}, 活跃会员: {ActiveAgentCount}, 会员总数: {TotalAgentCount}",
            totalValidTurnover, totalRebateAmount, activeAgentCount, totalAgentCount);

        return (totalValidTurnover, totalRebateAmount, activeAgentCount, totalAgentCount);
    }
}
