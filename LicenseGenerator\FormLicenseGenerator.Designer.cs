namespace LicenseGenerator;

partial class FormLicenseGenerator
{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }

    #region Windows Form Designer generated code

    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormLicenseGenerator));
        groupBox1 = new GroupBox();
        textBox_MachineCode = new TextBox();
        label1 = new Label();
        groupBox2 = new GroupBox();
        checkBox_IsPermanent = new CheckBox();
        dateTimePicker_ExpiresAt = new DateTimePicker();
        label6 = new Label();
        comboBox_LicenseType = new ComboBox();
        label5 = new Label();
        numericUpDown_MaxUsers = new NumericUpDown();
        label4 = new Label();
        textBox_ProductVersion = new TextBox();
        label3 = new Label();
        textBox_LicensedTo = new TextBox();
        label2 = new Label();
        groupBox3 = new GroupBox();
        textBox_SerialNumber = new TextBox();
        button_GenerateSerial = new Button();
        label7 = new Label();
        textBox_Output = new TextBox();
        label8 = new Label();
        groupBox1.SuspendLayout();
        groupBox2.SuspendLayout();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_MaxUsers).BeginInit();
        groupBox3.SuspendLayout();
        SuspendLayout();
        // 
        // groupBox1
        // 
        groupBox1.Controls.Add(textBox_MachineCode);
        groupBox1.Controls.Add(label1);
        groupBox1.Location = new Point(9, 10);
        groupBox1.Margin = new Padding(2, 3, 2, 3);
        groupBox1.Name = "groupBox1";
        groupBox1.Padding = new Padding(2, 3, 2, 3);
        groupBox1.Size = new Size(591, 68);
        groupBox1.TabIndex = 0;
        groupBox1.TabStop = false;
        groupBox1.Text = "机器码信息";
        // 
        // textBox_MachineCode
        // 
        textBox_MachineCode.Location = new Point(62, 26);
        textBox_MachineCode.Margin = new Padding(2, 3, 2, 3);
        textBox_MachineCode.Name = "textBox_MachineCode";
        textBox_MachineCode.Size = new Size(429, 23);
        textBox_MachineCode.TabIndex = 2;
        // 
        // label1
        // 
        label1.AutoSize = true;
        label1.Location = new Point(12, 28);
        label1.Margin = new Padding(2, 0, 2, 0);
        label1.Name = "label1";
        label1.Size = new Size(56, 17);
        label1.TabIndex = 0;
        label1.Text = "机器码：";
        // 
        // groupBox2
        // 
        groupBox2.Controls.Add(checkBox_IsPermanent);
        groupBox2.Controls.Add(dateTimePicker_ExpiresAt);
        groupBox2.Controls.Add(label6);
        groupBox2.Controls.Add(comboBox_LicenseType);
        groupBox2.Controls.Add(label5);
        groupBox2.Controls.Add(numericUpDown_MaxUsers);
        groupBox2.Controls.Add(label4);
        groupBox2.Controls.Add(textBox_ProductVersion);
        groupBox2.Controls.Add(label3);
        groupBox2.Controls.Add(textBox_LicensedTo);
        groupBox2.Controls.Add(label2);
        groupBox2.Location = new Point(9, 83);
        groupBox2.Margin = new Padding(2, 3, 2, 3);
        groupBox2.Name = "groupBox2";
        groupBox2.Padding = new Padding(2, 3, 2, 3);
        groupBox2.Size = new Size(591, 153);
        groupBox2.TabIndex = 1;
        groupBox2.TabStop = false;
        groupBox2.Text = "授权信息";
        // 
        // checkBox_IsPermanent
        // 
        checkBox_IsPermanent.AutoSize = true;
        checkBox_IsPermanent.Location = new Point(311, 119);
        checkBox_IsPermanent.Margin = new Padding(2, 3, 2, 3);
        checkBox_IsPermanent.Name = "checkBox_IsPermanent";
        checkBox_IsPermanent.Size = new Size(75, 21);
        checkBox_IsPermanent.TabIndex = 10;
        checkBox_IsPermanent.Text = "永久授权";
        checkBox_IsPermanent.UseVisualStyleBackColor = true;
        checkBox_IsPermanent.CheckedChanged += checkBox_IsPermanent_CheckedChanged;
        // 
        // dateTimePicker_ExpiresAt
        // 
        dateTimePicker_ExpiresAt.Location = new Point(78, 119);
        dateTimePicker_ExpiresAt.Margin = new Padding(2, 3, 2, 3);
        dateTimePicker_ExpiresAt.Name = "dateTimePicker_ExpiresAt";
        dateTimePicker_ExpiresAt.Size = new Size(219, 23);
        dateTimePicker_ExpiresAt.TabIndex = 9;
        // 
        // label6
        // 
        label6.AutoSize = true;
        label6.Location = new Point(12, 123);
        label6.Margin = new Padding(2, 0, 2, 0);
        label6.Name = "label6";
        label6.Size = new Size(68, 17);
        label6.TabIndex = 8;
        label6.Text = "到期时间：";
        // 
        // comboBox_LicenseType
        // 
        comboBox_LicenseType.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox_LicenseType.FormattingEnabled = true;
        comboBox_LicenseType.Location = new Point(373, 85);
        comboBox_LicenseType.Margin = new Padding(2, 3, 2, 3);
        comboBox_LicenseType.Name = "comboBox_LicenseType";
        comboBox_LicenseType.Size = new Size(118, 25);
        comboBox_LicenseType.TabIndex = 7;
        // 
        // label5
        // 
        label5.AutoSize = true;
        label5.Location = new Point(311, 88);
        label5.Margin = new Padding(2, 0, 2, 0);
        label5.Name = "label5";
        label5.Size = new Size(68, 17);
        label5.TabIndex = 6;
        label5.Text = "授权类型：";
        // 
        // numericUpDown_MaxUsers
        // 
        numericUpDown_MaxUsers.Location = new Point(78, 85);
        numericUpDown_MaxUsers.Margin = new Padding(2, 3, 2, 3);
        numericUpDown_MaxUsers.Maximum = new decimal(new int[] { 9999, 0, 0, 0 });
        numericUpDown_MaxUsers.Minimum = new decimal(new int[] { 1, 0, 0, int.MinValue });
        numericUpDown_MaxUsers.Name = "numericUpDown_MaxUsers";
        numericUpDown_MaxUsers.Size = new Size(117, 23);
        numericUpDown_MaxUsers.TabIndex = 5;
        numericUpDown_MaxUsers.Value = new decimal(new int[] { 1, 0, 0, 0 });
        // 
        // label4
        // 
        label4.AutoSize = true;
        label4.Location = new Point(12, 88);
        label4.Margin = new Padding(2, 0, 2, 0);
        label4.Name = "label4";
        label4.Size = new Size(68, 17);
        label4.TabIndex = 4;
        label4.Text = "最大用户：";
        // 
        // textBox_ProductVersion
        // 
        textBox_ProductVersion.Location = new Point(373, 51);
        textBox_ProductVersion.Margin = new Padding(2, 3, 2, 3);
        textBox_ProductVersion.Name = "textBox_ProductVersion";
        textBox_ProductVersion.Size = new Size(118, 23);
        textBox_ProductVersion.TabIndex = 3;
        textBox_ProductVersion.Text = "1.0.0";
        // 
        // label3
        // 
        label3.AutoSize = true;
        label3.Location = new Point(311, 54);
        label3.Margin = new Padding(2, 0, 2, 0);
        label3.Name = "label3";
        label3.Size = new Size(68, 17);
        label3.TabIndex = 2;
        label3.Text = "产品版本：";
        // 
        // textBox_LicensedTo
        // 
        textBox_LicensedTo.Location = new Point(78, 51);
        textBox_LicensedTo.Margin = new Padding(2, 3, 2, 3);
        textBox_LicensedTo.Name = "textBox_LicensedTo";
        textBox_LicensedTo.Size = new Size(219, 23);
        textBox_LicensedTo.TabIndex = 1;
        // 
        // label2
        // 
        label2.AutoSize = true;
        label2.Location = new Point(12, 54);
        label2.Margin = new Padding(2, 0, 2, 0);
        label2.Name = "label2";
        label2.Size = new Size(68, 17);
        label2.TabIndex = 0;
        label2.Text = "授权用户：";
        // 
        // groupBox3
        // 
        groupBox3.Controls.Add(textBox_SerialNumber);
        groupBox3.Controls.Add(button_GenerateSerial);
        groupBox3.Controls.Add(label7);
        groupBox3.Location = new Point(9, 241);
        groupBox3.Margin = new Padding(2, 3, 2, 3);
        groupBox3.Name = "groupBox3";
        groupBox3.Padding = new Padding(2, 3, 2, 3);
        groupBox3.Size = new Size(591, 68);
        groupBox3.TabIndex = 2;
        groupBox3.TabStop = false;
        groupBox3.Text = "序列号";
        // 
        // textBox_SerialNumber
        // 
        textBox_SerialNumber.Location = new Point(62, 26);
        textBox_SerialNumber.Margin = new Padding(2, 3, 2, 3);
        textBox_SerialNumber.Name = "textBox_SerialNumber";
        textBox_SerialNumber.Size = new Size(301, 23);
        textBox_SerialNumber.TabIndex = 2;
        // 
        // button_GenerateSerial
        // 
        button_GenerateSerial.Location = new Point(398, 24);
        button_GenerateSerial.Margin = new Padding(2, 3, 2, 3);
        button_GenerateSerial.Name = "button_GenerateSerial";
        button_GenerateSerial.Size = new Size(93, 26);
        button_GenerateSerial.TabIndex = 1;
        button_GenerateSerial.Text = "计算序列号";
        button_GenerateSerial.UseVisualStyleBackColor = true;
        button_GenerateSerial.Click += button_GenerateSerial_Click;
        // 
        // label7
        // 
        label7.AutoSize = true;
        label7.Location = new Point(12, 28);
        label7.Margin = new Padding(2, 0, 2, 0);
        label7.Name = "label7";
        label7.Size = new Size(56, 17);
        label7.TabIndex = 0;
        label7.Text = "序列号：";
        // 
        // textBox_Output
        // 
        textBox_Output.Location = new Point(9, 340);
        textBox_Output.Margin = new Padding(2, 3, 2, 3);
        textBox_Output.Multiline = true;
        textBox_Output.Name = "textBox_Output";
        textBox_Output.ReadOnly = true;
        textBox_Output.ScrollBars = ScrollBars.Vertical;
        textBox_Output.Size = new Size(496, 145);
        textBox_Output.TabIndex = 5;
        // 
        // label8
        // 
        label8.AutoSize = true;
        label8.Location = new Point(9, 320);
        label8.Margin = new Padding(2, 0, 2, 0);
        label8.Name = "label8";
        label8.Size = new Size(68, 17);
        label8.TabIndex = 6;
        label8.Text = "操作日志：";
        // 
        // FormLicenseGenerator
        // 
        AutoScaleDimensions = new SizeF(7F, 17F);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(516, 493);
        Controls.Add(label8);
        Controls.Add(textBox_Output);
        Controls.Add(groupBox3);
        Controls.Add(groupBox2);
        Controls.Add(groupBox1);
        FormBorderStyle = FormBorderStyle.FixedSingle;
        Icon = (Icon)resources.GetObject("$this.Icon");
        Margin = new Padding(2, 3, 2, 3);
        MaximizeBox = false;
        Name = "FormLicenseGenerator";
        StartPosition = FormStartPosition.CenterScreen;
        Text = "CommandGuard 注册机";
        groupBox1.ResumeLayout(false);
        groupBox1.PerformLayout();
        groupBox2.ResumeLayout(false);
        groupBox2.PerformLayout();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_MaxUsers).EndInit();
        groupBox3.ResumeLayout(false);
        groupBox3.PerformLayout();
        ResumeLayout(false);
        PerformLayout();
    }

    #endregion

    private GroupBox groupBox1;
    private TextBox textBox_MachineCode;
    private Label label1;
    private GroupBox groupBox2;
    private CheckBox checkBox_IsPermanent;
    private DateTimePicker dateTimePicker_ExpiresAt;
    private Label label6;
    private ComboBox comboBox_LicenseType;
    private Label label5;
    private NumericUpDown numericUpDown_MaxUsers;
    private Label label4;
    private TextBox textBox_ProductVersion;
    private Label label3;
    private TextBox textBox_LicensedTo;
    private Label label2;
    private GroupBox groupBox3;
    private TextBox textBox_SerialNumber;
    private Button button_GenerateSerial;
    private Label label7;
    private TextBox textBox_Output;
    private Label label8;
}
