﻿using CommandGuard.Models;

namespace CommandGuard.Interfaces.Business;

public interface IMemberService
{
    Task<int> AddMemberAsync(Member member);
    Task<int> UpdateMemberAsync(Member member);
    Task<bool> UpdateMemberNickNameAsync(string account, string newNickName);
    Task<bool> UpdateMemberRebatePercentAsync(string account, decimal newRebatePercent);
    Task<bool> UpdateMemberBalanceAsync(string account, decimal changeAmount, string remark);
    Task<bool> UpdateMemberUserTypeAsync(string account, string newUserType);
    Task<bool> SetMemberAgentAsync(string memberAccount, string agentName, decimal agentRebatePercent);
    Task<bool> RemoveMemberAgentAsync(string memberAccount);
    Task<int> DeleteMemberAsync(string account);
    Task<Member?> GetMemberAsync(string account);
    Task<List<Member>> GetMembersAsync();
    Task<Member> GetOrCreateMemberWithNickNameAsync(string account, string nickName = "", Func<string, Task<string>>? getNickNameFunc = null);
    Task<Member> CreateMemberWithAutoNickNameAsync(string account, string nickName = "");
}