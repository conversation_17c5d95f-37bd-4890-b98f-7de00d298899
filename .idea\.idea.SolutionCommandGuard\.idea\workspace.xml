<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile pubXmlPath="CommandGuard/Properties/PublishProfiles/FolderProfile.pubxml">CommandGuard/CommandGuard.csproj</projectFile>
    <projectFile>LicenseGenerator/LicenseGenerator.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="71898fc7-2e5d-4670-b569-cdb0d3982709" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/0f04514edab49ce421fdf1e12235d4b26bfd8cc473ef7f135bb285d607a9e8/ConsolePal.Windows.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/403bac1c2df7f4effa0f23f4e78849325ba87920969fb0f1217bbe44ec0d/IUpdate.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/aa36dbd3866a97a4fa3ffc4c63abc844365187604a18134ca52da99bdc2d90/ISelect0.cs" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="318yXijDWs9JwAiBogzHyhyu755" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;.NET 项目.CommandGuard.executor&quot;: &quot;Debug&quot;,
    &quot;.NET 项目.LicenseGenerator.executor&quot;: &quot;Debug&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;editor.preferences.smartKeys_rider&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;发布到文件夹.Publish CommandGuard to folder.executor&quot;: &quot;Run&quot;,
    &quot;发布到文件夹.Publish LicenseGenerator to folder.executor&quot;: &quot;Run&quot;
  }
}</component>
  <component name="RunManager" selected="发布到文件夹.Publish CommandGuard to folder">
    <configuration name="Publish CommandGuard to folder" type="DotNetFolderPublish" factoryName="Publish to folder">
      <riderPublish configuration="Release" delete_existing_files="true" platform="Any CPU" target_folder="$PROJECT_DIR$/CommandGuard/bin/Release/net8.0-windows/publish" target_framework="net8.0-windows" uuid_high="-6750403863562599266" uuid_low="-5317466096812465761">
        <runtimes>
          <item value="Portable" />
        </runtimes>
      </riderPublish>
      <method v="2" />
    </configuration>
    <configuration name="Publish LicenseGenerator to folder" type="DotNetFolderPublish" factoryName="Publish to folder">
      <riderPublish configuration="Release" platform="Any CPU" target_folder="$PROJECT_DIR$/LicenseGenerator/bin/Release/net8.0-windows/publish" target_framework="net8.0-windows" uuid_high="-6493110834444286206" uuid_low="-4665131492102159334">
        <runtimes>
          <item value="Portable" />
        </runtimes>
      </riderPublish>
      <method v="2" />
    </configuration>
    <configuration name="CommandGuard: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/CommandGuard/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="-6750403863562599266" uuid_low="-5317466096812465761" />
      <method v="2" />
    </configuration>
    <configuration name="CommandGuard" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/CommandGuard/CommandGuard.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="LicenseGenerator" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/LicenseGenerator/LicenseGenerator.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <list>
      <item itemvalue=".NET 项目.CommandGuard" />
      <item itemvalue=".NET 项目.LicenseGenerator" />
      <item itemvalue="发布到 IIS.CommandGuard: FolderProfile" />
      <item itemvalue="发布到文件夹.Publish LicenseGenerator to folder" />
      <item itemvalue="发布到文件夹.Publish CommandGuard to folder" />
    </list>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="71898fc7-2e5d-4670-b569-cdb0d3982709" name="更改" comment="" />
      <created>1754920037845</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754920037845</updated>
      <workItem from="1754920039631" duration="6169000" />
      <workItem from="1754926752517" duration="1387000" />
      <workItem from="1754928526198" duration="23981000" />
      <workItem from="1755001913587" duration="40756000" />
      <workItem from="1755096990945" duration="22232000" />
      <workItem from="1755171290096" duration="21450000" />
      <workItem from="1755309658890" duration="3772000" />
      <workItem from="1755358638599" duration="11782000" />
      <workItem from="1755390501993" duration="2073000" />
      <workItem from="1755405413083" duration="2991000" />
      <workItem from="1755409657506" duration="2928000" />
      <workItem from="1755434466803" duration="2059000" />
      <workItem from="1755444133653" duration="1123000" />
      <workItem from="1755445758770" duration="3844000" />
      <workItem from="1755522424106" duration="46000" />
      <workItem from="1755559853877" duration="7369000" />
      <workItem from="1755782302204" duration="221000" />
      <workItem from="1755782533391" duration="253000" />
      <workItem from="1755783513342" duration="4412000" />
      <workItem from="1755817545255" duration="21215000" />
      <workItem from="1755903375808" duration="28871000" />
      <workItem from="1755946527859" duration="13386000" />
      <workItem from="1755961537915" duration="1021000" />
      <workItem from="1755964186179" duration="1218000" />
      <workItem from="1755987852570" duration="267000" />
      <workItem from="1756025628034" duration="15156000" />
      <workItem from="1756046176543" duration="893000" />
      <workItem from="1756383955037" duration="4636000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>