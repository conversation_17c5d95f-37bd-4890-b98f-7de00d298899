using CommandGuard.Configuration;
using CommandGuard.Constants;
using CommandGuard.Enums;
using CommandGuard.Services.Infrastructure;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Forms;

/// <summary>
/// 启动配置窗体 - 用于在程序启动时选择聊天平台
/// 
/// 功能说明：
/// - 显示所有可用的聊天平台选项
/// - 用户选择平台后更新配置
/// - 验证配置有效性
/// - 进入主窗体
/// 
/// 设计目标：
/// - 提供友好的平台选择界面
/// - 确保配置的正确性
/// - 支持配置的持久化保存
/// - 便于后续扩展其他启动配置
/// </summary>
public partial class FormStartup : Form
{
    #region 私有字段

    /// <summary>
    /// 日志记录器
    /// </summary>
    private readonly ILogger<FormStartup> _logger;

    /// <summary>
    /// 服务提供者 - 用于创建主窗体
    /// </summary>
    private readonly IServiceProvider _serviceProvider;

    /// <summary>
    /// 配置管理服务
    /// </summary>
    private readonly IConfigurationService _configurationService;

    /// <summary>
    /// 当前选择的聊天平台
    /// </summary>
    private EnumChatApp _selectedChatApp = EnumChatApp.一起聊吧;

    #endregion

    #region 构造函数

    /// <summary>
    /// 启动配置窗体构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="configurationService">配置管理服务</param>
    public FormStartup(ILogger<FormStartup> logger, IServiceProvider serviceProvider, IConfigurationService configurationService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _configurationService = configurationService ?? throw new ArgumentNullException(nameof(configurationService));

        InitializeComponent();

        // 同步加载配置，确保在UI初始化前完成
        LoadConfigurationSync();
        InitializeForm();
        InitializeLogo();

        _logger.LogInformation(@"启动配置窗体已创建");
    }

    #endregion

    #region 窗体初始化

    /// <summary>
    /// 同步加载配置
    /// </summary>
    private void LoadConfigurationSync()
    {
        try
        {
            // 使用Task.Run避免阻塞UI线程，但同步等待结果
            var task = Task.Run(async () => await _configurationService.GetSelectedChatAppAsync());
            _selectedChatApp = task.GetAwaiter().GetResult();

            // 同时更新运行时配置
            RuntimeConfiguration.SelectedChatApp = _selectedChatApp;

            _logger.LogInformation(@"配置加载完成，平台: {Platform}", _selectedChatApp);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"加载配置失败，使用默认配置");

            // 使用默认配置
            _selectedChatApp = EnumChatApp.一起聊吧;
            RuntimeConfiguration.SelectedChatApp = _selectedChatApp;
        }
    }

    /// <summary>
    /// 初始化窗体设置
    /// </summary>
    private void InitializeForm()
    {
        try
        {
            // 设置窗体属性
            Text = @"请选择要使用的聊天平台";
            StartPosition = FormStartPosition.CenterScreen;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Size = new Size(484, 361);

            // 初始化平台选择（此时_selectedChatApp已经从配置加载）
            InitializePlatformSelection();

            _logger.LogDebug(@"窗体初始化完成，当前平台: {Platform}", _selectedChatApp);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"窗体初始化失败");
            throw;
        }
    }

    /// <summary>
    /// 初始化聊天平台选择控件
    /// </summary>
    private void InitializePlatformSelection()
    {
        try
        {
            // 清空现有选项
            comboBox_Platform.Items.Clear();

            // 添加所有聊天平台选项
            var platforms = Enum.GetValues<EnumChatApp>();
            foreach (var platform in platforms)
            {
                comboBox_Platform.Items.Add(new PlatformItem(platform));
            }

            // 设置当前选择
            var currentItem = comboBox_Platform.Items.Cast<PlatformItem>()
                .FirstOrDefault(item => item.Platform == _selectedChatApp);
            if (currentItem != null)
            {
                comboBox_Platform.SelectedItem = currentItem;
                _logger.LogInformation(@"平台选择已设置为配置中的值: {Platform}", _selectedChatApp);
            }
            else if (comboBox_Platform.Items.Count > 0)
            {
                comboBox_Platform.SelectedIndex = 0;
                _logger.LogWarning(@"未找到配置中的平台 {Platform}，使用默认选择", _selectedChatApp);
            }


            _logger.LogDebug(@"平台选择控件初始化完成，共 {Count} 个平台", platforms.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"初始化平台选择控件失败");
            throw;
        }
    }

    /// <summary>
    /// 更新平台选择
    /// </summary>
    private void UpdatePlatformSelection()
    {
        if (comboBox_Platform.SelectedItem is PlatformItem selectedItem)
        {
            _selectedChatApp = selectedItem.Platform;
            _logger.LogDebug(@"平台选择已更新: {Platform}", _selectedChatApp);
        }
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// 平台选择变更事件
    /// </summary>
    private void ComboBox_Platform_SelectedIndexChanged(object sender, EventArgs e)
    {
        try
        {
            UpdatePlatformSelection();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"处理平台选择变更事件失败");
        }
    }

    /// <summary>
    /// 确认按钮点击事件
    /// </summary>
    private async void Button_Confirm_Click(object sender, EventArgs e)
    {
        try
        {
            button_Confirm.Enabled = false;
            button_Cancel.Enabled = false;

            _logger.LogInformation($@"用户选择聊天平台: {_selectedChatApp}");

            // 更新配置（这里可以扩展为保存到配置文件）
            await UpdateConfigurationAsync(_selectedChatApp);

            // 创建并显示主窗体
            await ShowMainFormAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"确认平台选择失败");
            MessageBox.Show($@"配置失败：{ex.Message}", @"错误",
                MessageBoxButtons.OK, MessageBoxIcon.Error);

            button_Confirm.Enabled = true;
            button_Cancel.Enabled = true;
        }
    }

    /// <summary>
    /// 取消按钮点击事件
    /// </summary>
    private void Button_Cancel_Click(object sender, EventArgs e)
    {
        _logger.LogInformation(@"用户取消平台选择，程序退出");
        Application.Exit();
    }

    #endregion

    #region 业务逻辑

    /// <summary>
    /// 更新配置
    /// </summary>
    /// <param name="platform">选择的平台</param>
    private async Task UpdateConfigurationAsync(EnumChatApp platform)
    {
        try
        {
            // 更新运行时配置
            RuntimeConfiguration.SelectedChatApp = platform;

            // 保存到配置文件，实现记忆功能
            await _configurationService.SaveSelectedChatAppAsync(platform);

            _logger.LogInformation(@"配置更新完成，平台: {Platform}，已保存到配置文件", platform);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"更新配置失败");
            throw;
        }
    }

    /// <summary>
    /// 显示主窗体
    /// </summary>
    private async Task ShowMainFormAsync()
    {
        try
        {
            _logger.LogInformation(@"正在创建主窗体...");

            // 从服务容器获取主窗体
            var mainForm = _serviceProvider.GetRequiredService<FormMain>();

            // 隐藏当前窗体
            Hide();

            // 显示主窗体
            mainForm.Show();

            _logger.LogInformation(@"主窗体已显示");
            await Task.Delay(100); // 确保窗体切换完成
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"显示主窗体失败");
            throw;
        }
    }

    #endregion

    #region 辅助类

    /// <summary>
    /// 平台选项项 - 用于ComboBox显示
    /// </summary>
    private class PlatformItem(EnumChatApp platform)
    {
        public EnumChatApp Platform { get; } = platform;

        public override string ToString()
        {
            return Platform.ToString();
        }
    }

    #endregion

    #region 图标加载

    /// <summary>
    /// 初始化Logo图片
    /// </summary>
    private void InitializeLogo()
    {
        try
        {
            // 首先尝试从ImageConstants中获取logo路径
            try
            {
                if (ImageConstants.ImagePaths.TryGetValue("logo", out var logoPath) && File.Exists(logoPath))
                {
                    pictureBox_Logo.Image = Image.FromFile(logoPath);
                    pictureBox_Logo.SizeMode = PictureBoxSizeMode.Zoom;
                    _logger.LogInformation(@"成功加载Logo图片: {ImagePath}", logoPath);
                    return;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, @"从ImageConstants加载Logo失败，尝试备用路径");
            }

            // 备用路径：定义可能的图片文件路径
            var imagePaths = new[]
            {
                @"img\logo.png",
                @"img\logo.jpg",
                @"img\logo.jpeg",
                @"img\logo.bmp",
                @"img\logo.gif",
                @"Images\logo.png",
                @"Images\logo.jpg",
                @"Images\logo.jpeg",
                @"Images\logo.bmp",
                @"Images\logo.gif",
                @"logo.png",
                @"logo.jpg",
                @"logo.jpeg",
                @"logo.bmp",
                @"logo.gif"
            };

            // 尝试加载图片文件
            foreach (var imagePath in imagePaths)
            {
                if (File.Exists(imagePath))
                {
                    try
                    {
                        pictureBox_Logo.Image = Image.FromFile(imagePath);
                        pictureBox_Logo.SizeMode = PictureBoxSizeMode.Zoom;
                        _logger.LogInformation(@"成功加载Logo图片: {ImagePath}", imagePath);
                        return;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, @"加载图片文件失败: {ImagePath}", imagePath);
                    }
                }
            }

            // 如果没有找到图片文件，使用默认图标
            SetDefaultLogo();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"初始化Logo时发生错误");
            SetDefaultLogo();
        }
    }

    /// <summary>
    /// 设置默认Logo（使用系统图标）
    /// </summary>
    private void SetDefaultLogo()
    {
        try
        {
            // 使用系统应用程序图标作为默认Logo
            pictureBox_Logo.Image = SystemIcons.Application.ToBitmap();
            pictureBox_Logo.SizeMode = PictureBoxSizeMode.CenterImage;
            _logger.LogInformation(@"使用默认系统图标作为Logo");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"设置默认Logo时发生错误");
        }
    }

    #endregion
}