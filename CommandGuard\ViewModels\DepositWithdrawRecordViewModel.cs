using System.ComponentModel;
using CommandGuard.Enums;

namespace CommandGuard.ViewModels;

/// <summary>
/// 上下分记录视图模型
/// 用于在上下分记录查询界面显示申请数据
/// </summary>
public class DepositWithdrawRecordViewModel : INotifyPropertyChanged
{
    private long _id;
    private string _orderNumber = string.Empty;
    private decimal _totalDeposit;
    private decimal _totalWithdraw;
    private string _nickName = string.Empty;
    private string _account = string.Empty;
    private string _type = string.Empty;
    private decimal _amount;
    private DateTime _createdTime;
    private EnumDepositStatus _status;
    private DateTime? _processedTime;
    private bool _isFakeUser;

    /// <summary>
    /// 申请记录ID
    /// </summary>
    public long Id
    {
        get => _id;
        set
        {
            _id = value;
            OnPropertyChanged(nameof(Id));
        }
    }

    /// <summary>
    /// 单号
    /// 唯一标识每笔上下分申请的编号
    /// </summary>
    public string OrderNumber
    {
        get => _orderNumber;
        set
        {
            _orderNumber = value ?? string.Empty;
            OnPropertyChanged(nameof(OrderNumber));
        }
    }

    /// <summary>
    /// 总上分
    /// 该会员的累计上分金额
    /// </summary>
    public decimal TotalDeposit
    {
        get => _totalDeposit;
        set
        {
            _totalDeposit = value;
            OnPropertyChanged(nameof(TotalDeposit));
        }
    }

    /// <summary>
    /// 总下分
    /// 该会员的累计下分金额
    /// </summary>
    public decimal TotalWithdraw
    {
        get => _totalWithdraw;
        set
        {
            _totalWithdraw = value;
            OnPropertyChanged(nameof(TotalWithdraw));
        }
    }

    /// <summary>
    /// 昵称
    /// 用户昵称
    /// </summary>
    public string NickName
    {
        get => _nickName;
        set
        {
            _nickName = value ?? string.Empty;
            OnPropertyChanged(nameof(NickName));
        }
    }

    /// <summary>
    /// 账号
    /// 用户账号
    /// </summary>
    public string Account
    {
        get => _account;
        set
        {
            _account = value ?? string.Empty;
            OnPropertyChanged(nameof(Account));
        }
    }

    /// <summary>
    /// 类型
    /// 上分或下分
    /// </summary>
    public string Type
    {
        get => _type;
        set
        {
            _type = value ?? string.Empty;
            OnPropertyChanged(nameof(Type));
        }
    }

    /// <summary>
    /// 积分
    /// 申请记录的上分或下分金额
    /// </summary>
    public decimal Amount
    {
        get => _amount;
        set
        {
            _amount = value;
            OnPropertyChanged(nameof(Amount));
        }
    }

    /// <summary>
    /// 创建时间
    /// 申请提交时间
    /// </summary>
    public DateTime CreatedTime
    {
        get => _createdTime;
        set
        {
            _createdTime = value;
            OnPropertyChanged(nameof(CreatedTime));
            OnPropertyChanged(nameof(CreatedTimeText));
        }
    }

    /// <summary>
    /// 创建时间文本
    /// 用于界面显示的时间格式
    /// </summary>
    public string CreatedTimeText => _createdTime.ToString(@"yyyy-MM-dd HH:mm:ss");

    /// <summary>
    /// 处理状态
    /// 申请的处理状态
    /// </summary>
    public EnumDepositStatus Status
    {
        get => _status;
        set
        {
            _status = value;
            OnPropertyChanged(nameof(Status));
            OnPropertyChanged(nameof(StatusText));
        }
    }

    /// <summary>
    /// 处理状态文本
    /// 用于界面显示的状态文本
    /// </summary>
    public string StatusText
    {
        get
        {
            return _status switch
            {
                EnumDepositStatus.Pending => @"待处理",
                EnumDepositStatus.Approved => @"已通过",
                EnumDepositStatus.Rejected => @"已拒绝",
                _ => @"未知"
            };
        }
    }

    /// <summary>
    /// 处理时间
    /// 申请被处理的时间
    /// </summary>
    public DateTime? ProcessedTime
    {
        get => _processedTime;
        set
        {
            _processedTime = value;
            OnPropertyChanged(nameof(ProcessedTime));
            OnPropertyChanged(nameof(ProcessedTimeText));
        }
    }

    /// <summary>
    /// 处理时间文本
    /// 用于界面显示的处理时间格式
    /// </summary>
    public string ProcessedTimeText => _processedTime?.ToString(@"yyyy-MM-dd HH:mm:ss") ?? @"-";

    /// <summary>
    /// 是否为假人
    /// 标识该用户是否为假人
    /// </summary>
    public bool IsFakeUser
    {
        get => _isFakeUser;
        set
        {
            _isFakeUser = value;
            OnPropertyChanged(nameof(IsFakeUser));
            OnPropertyChanged(nameof(IsFakeUserText));
        }
    }

    /// <summary>
    /// 是否为假人文本
    /// 用于界面显示的假人标识文本
    /// </summary>
    public string IsFakeUserText => _isFakeUser ? @"是" : @"否";

    /// <summary>
    /// 净上分金额
    /// 总上分 - 总下分
    /// </summary>
    public decimal NetDeposit => _totalDeposit - _totalWithdraw;

    /// <summary>
    /// 净上分金额文本
    /// 用于界面显示，正数显示为绿色，负数显示为红色
    /// </summary>
    public string NetDepositText
    {
        get
        {
            var net = NetDeposit;
            return net >= 0 ? $@"+{net:F2}" : $@"{net:F2}";
        }
    }

    /// <summary>
    /// 是否已处理
    /// </summary>
    public bool IsProcessed => _status != EnumDepositStatus.Pending;

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
