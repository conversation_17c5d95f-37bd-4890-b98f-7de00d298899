using CommandGuard.ViewModels;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Forms;

/// <summary>
/// 主窗体 - 拉手返点页面功能
/// </summary>
public partial class FormMain
{
    #region 拉手返点查询事件处理

    /// <summary>
    /// 拉手返点查询按钮点击事件处理
    /// </summary>
    private async void button_根据条件查询拉手返点_Click(object? sender, EventArgs e)
    {
        try
        {
            // 禁用查询按钮，防止重复点击
            button_根据条件查询拉手返点.Enabled = false;
            button_根据条件查询拉手返点.Text = @"查询中...";

            // 获取查询条件
            var queryConditions = GetAgentRebateQueryConditions();

            // 验证查询条件
            if (!ValidateAgentRebateQueryConditions(queryConditions))
            {
                return;
            }

            // 执行查询
            await QueryAgentRebateRecordsAsync(
                queryConditions.StartTime,
                queryConditions.EndTime,
                queryConditions.AgentName
            );

            _logger.LogInformation(@"拉手返点查询完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"查询拉手返点时发生错误");
            MessageBox.Show($@"查询失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            // 恢复查询按钮状态
            button_根据条件查询拉手返点.Enabled = true;
            button_根据条件查询拉手返点.Text = @"查询统计";
        }
    }

    /// <summary>
    /// 拉手返点查询文本框键盘事件处理
    /// 支持Enter键快捷查询
    /// </summary>
    private void AgentRebateQueryTextBox_KeyDown(object? sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Enter)
        {
            e.Handled = true; // 阻止默认的Enter键行为

            // 触发查询
            Task.Run(() =>
            {
                // 模拟点击查询按钮
                Invoke(() => { button_根据条件查询拉手返点.PerformClick(); });
            });
        }
    }

    #endregion

    #region 拉手返点查询功能

    /// <summary>
    /// 获取拉手返点查询条件
    /// </summary>
    /// <returns>拉手返点查询条件对象</returns>
    private AgentRebateQueryConditions GetAgentRebateQueryConditions()
    {
        try
        {
            // 构建开始时间
            var startDate = dateTimePicker_查询拉手返点开始日期.Value.Date;
            var startTime = startDate
                .AddHours((double)numericUpDown_查询拉手返点开始小时.Value)
                .AddMinutes((double)numericUpDown_查询拉手返点开始分钟.Value);

            // 构建结束时间
            var endDate = dateTimePicker_查询拉手返点结束日期.Value.Date;
            var endTime = endDate
                .AddHours((double)numericUpDown_查询拉手返点结束小时.Value)
                .AddMinutes((double)numericUpDown_查询拉手返点结束分钟.Value);

            // 获取拉手名称
            var agentName = string.IsNullOrWhiteSpace(textBox_拉手名称.Text) ? null : textBox_拉手名称.Text.Trim();

            return new AgentRebateQueryConditions
            {
                StartTime = startTime,
                EndTime = endTime,
                AgentName = agentName
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"获取拉手返点查询条件时发生错误");
            throw new InvalidOperationException(@"获取查询条件失败", ex);
        }
    }

    /// <summary>
    /// 验证拉手返点查询条件
    /// </summary>
    /// <param name="queryConditions">查询条件</param>
    /// <returns>验证是否通过</returns>
    private bool ValidateAgentRebateQueryConditions(AgentRebateQueryConditions queryConditions)
    {
        try
        {
            var (isValid, errorMessage) = queryConditions.Validate();

            if (!isValid)
            {
                MessageBox.Show(errorMessage, @"查询条件错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            _logger.LogDebug(@"拉手返点查询条件验证通过: {Description}", queryConditions.GetDescription());
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"验证拉手返点查询条件时发生错误");
            MessageBox.Show(@"验证查询条件时发生错误", @"系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            return false;
        }
    }

    #endregion

    #region 拉手返点查询功能

    /// <summary>
    /// 查询拉手返点记录
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="agentName">拉手名称（可选）</param>
    private async Task QueryAgentRebateRecordsAsync(DateTime startTime, DateTime endTime, string? agentName = null)
    {
        try
        {
            _logger.LogInformation(@"开始查询拉手返点记录，时间范围: {StartTime} - {EndTime}", startTime, endTime);

            // 查询拉手返点记录
            _agentRebateRecordViewModels = await _agentRebateRecordService.QueryAgentRebateRecordsAsync(startTime, endTime, agentName);

            // 更新UI（确保在UI线程中执行）
            if (InvokeRequired)
            {
                Invoke(() =>
                {
                    _agentRebateRecordBindingSource.DataSource = _agentRebateRecordViewModels;
                    _agentRebateRecordBindingSource.ResetBindings(false);

                    // 清除选择
                    dataGridView_拉手返点.ClearSelection();

                    _logger.LogInformation(@"拉手返点记录查询完成，共 {Count} 条记录", _agentRebateRecordViewModels.Count);
                });
            }
            else
            {
                _agentRebateRecordBindingSource.DataSource = _agentRebateRecordViewModels;
                _agentRebateRecordBindingSource.ResetBindings(false);

                // 清除选择
                dataGridView_拉手返点.ClearSelection();

                _logger.LogInformation(@"拉手返点记录查询完成，共 {Count} 条记录", _agentRebateRecordViewModels.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"查询拉手返点记录时发生错误");
            throw;
        }
    }



    #endregion
}
