namespace CommandGuard.Interfaces.Infrastructure;

/// <summary>
/// HTTP API服务接口
/// 负责启动和管理HTTP监听服务
/// 提供外部消息接收的API端点
/// 继承IDisposable确保资源正确释放
/// </summary>
public interface IHttpApiService : IDisposable
{
    /// <summary>
    /// 启动HTTP监听服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止HTTP监听服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    Task StopAsync(CancellationToken cancellationToken = default);
}