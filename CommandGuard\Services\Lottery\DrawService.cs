﻿using CommandGuard.Interfaces.Infrastructure;
using CommandGuard.Interfaces.Lottery;
using CommandGuard.Models;
using Flurl.Http;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Lottery;

/// <summary>
/// 开奖服务类 - 彩票开奖信息处理的核心业务服务
/// </summary>
public class DrawService(
    ILogger<DrawService> logger,
    IFreeSql fSql,
    ILotteryConfigurationService lotteryConfig,
    ISystemSettingService systemSettingService) : IDrawService
{
    /// <summary>
    /// 用于获取开奖数据API地址列表
    /// 包含多个服务器地址，用于提高数据获取的可靠性
    /// </summary>
    private async Task<List<string>> GetDataUrlListAsync()
    {
        try
        {
            var platformHost = await systemSettingService.GetSettingValueAsync<string>("PlatformHost", "http://127.0.0.1:6000");
            return [$"{platformHost}/DrawInfo/"];
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取平台地址失败，使用默认地址");
            return ["http://127.0.0.1:6000/DrawInfo/"];
        }
    }

    /// <summary>
    /// 获取开奖信息 - 从API获取并保存开奖数据
    ///
    /// 功能：
    /// - 从多个API地址获取开奖数据
    /// - 解析JSON格式的开奖信息
    /// - 检查是否有新的开奖数据
    /// - 保存新数据到数据库
    /// - 支持多服务器容错
    ///
    /// 数据处理：
    /// - 按期号去重，避免重复保存
    /// - 验证开奖号码格式
    /// - 记录详细的操作日志
    ///
    /// 返回值：true表示有新数据，false表示无新数据
    /// </summary>
    /// <param name="token">取消令牌</param>
    /// <returns>是否获取到新的开奖数据</returns>
    public async Task<bool> GetDrawFromApiAsync(CancellationToken token)
    {
        bool hasNewDrawData = false;
        var dataUrlList = await GetDataUrlListAsync();
        foreach (string url in dataUrlList)
        {
            try
            {
                // 发送请求获取数据
                ResObj resObj = await url.WithTimeout(TimeSpan.FromSeconds(3))
                    .PostJsonAsync(new
                    {
                        Lottery = lotteryConfig.CurrentLottery
                    }, cancellationToken: token)
                    .ReceiveJson<ResObj>();

                // 获取数据异常，跳过
                if (resObj == null)
                {
                    continue;
                }

                // 获取数据异常，记录日志
                if (resObj.StatusCode != 0)
                {
                    logger.LogWarning("获取开奖信息异常: {Message}", resObj.Msg);
                    continue;
                }

                // 没有数据，跳过
                if (!resObj.DrawList.Any())
                {
                    continue;
                }

                // 遍历取出数据
                List<KaiJiang> kjList = [];
                foreach (Draw draw in resObj.DrawList)
                {
                    KaiJiang kj = new KaiJiang
                    {
                        Issue = draw.PreDrawIssue, // 期号
                        DrawNum = draw.PreDrawCode, // 开奖号码
                        Time = draw.PreDrawTime // 开奖时间
                    };
                    kjList.Add(kj);
                }

                // 按开奖期号排序kjList
                kjList = kjList.OrderBy(x => x.Issue).ToList();

                // 遍历写入数据库，避免重复数据
                foreach (KaiJiang kj in kjList)
                {
                    // 数据库中不存在该期号数据，写入数据库
                    KaiJiang? kjInDb = await fSql.Select<KaiJiang>()
                        .Where(a => a.Issue.Equals(kj.Issue))
                        .ToOneAsync(token);

                    if (kjInDb == null)
                    {
                        await fSql.Insert<KaiJiang>().AppendData(kj).ExecuteIdentityAsync(token);
                        hasNewDrawData = true;
                    }
                }
            }
            catch (FlurlHttpTimeoutException ex)
            {
                logger.LogError(ex, "获取开奖信息超时");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "获取开奖信息异常");
            }
        }

        return hasNewDrawData;
    }

    /// <summary>
    /// 获取最新开奖信息 - 查询最近一期的开奖结果
    ///
    /// 功能：
    /// - 按期号降序排列查询
    /// - 返回最新一期的开奖信息
    /// - 支持异步操作和取消令牌
    ///
    /// 用途：
    /// - 显示最新开奖结果
    /// - 开奖状态检查
    /// - 历史数据查询
    /// </summary>
    /// <param name="token">取消令牌</param>
    /// <returns>最新的开奖信息</returns>
    public async Task<KaiJiang> GetDrawLastAsync(CancellationToken token)
    {
        var lastDraw = await fSql.Select<KaiJiang>()
            .OrderByDescending(x => x.Issue)
            .FirstAsync(token);
        return lastDraw;
    }

    /// <summary>
    /// 根据期号获取开奖信息
    /// </summary>
    /// <param name="issue">期号</param>
    /// <param name="token">取消令牌</param>
    /// <returns>开奖信息，如果不存在则返回null</returns>
    public async Task<KaiJiang?> GetDrawByIssueAsync(string issue, CancellationToken token)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(issue))
            {
                logger.LogWarning(@"获取开奖信息失败，期号为空");
                return null;
            }

            var draw = await fSql.Select<KaiJiang>()
                .Where(x => x.Issue == issue)
                .FirstAsync(token);

            return draw;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"根据期号获取开奖信息失败，期号: {Issue}", issue);
            return null;
        }
    }

    /// <summary>
    /// 清空开奖信息 - 删除所有开奖记录
    ///
    /// 功能：
    /// - 删除KaiJiang表中的所有开奖记录
    /// - 清空历史开奖数据
    /// - 支持异步操作和取消令牌
    ///
    /// ⚠️ 警告：
    /// - 这是危险操作，会删除所有开奖历史
    /// - 通常用于系统重置或测试环境清理
    ///
    /// 使用场景：
    /// - 系统初始化
    /// - 数据重置
    /// - 测试环境清理
    /// </summary>
    public Task ClearDrawAsync(CancellationToken token)
    {
        logger.LogInformation("清空开奖信息");
        return fSql.Delete<KaiJiang>().Where("1=1").ExecuteAffrowsAsync(token);
    }
}