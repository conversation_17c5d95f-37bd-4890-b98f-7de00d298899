using CommandGuard.Configuration;
using CommandGuard.Enums;

namespace CommandGuard.Helpers;

/// <summary>
/// 表情符号工具类 - 提供跨平台的表情符号支持
/// 
/// 功能说明：
/// - 根据配置的聊天平台提供适配的表情符号
/// - 统一管理所有表情符号的获取逻辑
/// - 支持多种聊天平台的表情符号格式
/// 
/// 设计优势：
/// - 简单的静态方法，无需依赖注入
/// - 轻量级实现，性能更好
/// - 易于使用和维护
/// </summary>
public static class EmojiHelper
{
    /// <summary>
    /// 获取钱包表情符号 - 用于财务相关消息
    ///
    /// 平台适配：
    /// - MyQQ：使用自定义格式
    /// - 一起聊吧：使用标准Unicode
    ///
    /// 用途：
    /// - 财务申请消息
    /// - 余额变更通知
    /// - 收益统计显示
    /// </summary>
    /// <returns>返回适配当前聊天平台的钱包表情符号字符串</returns>
    public static string GetMoneyBag()
    {
        return RuntimeConfiguration.SelectedChatApp switch
        {
            EnumChatApp.MyQQ => @"[emoji=F09F92B0]", // MyQQ平台使用自定义格式
            EnumChatApp.一起聊吧 => @"💰", // 一起聊吧平台支持直接Unicode
            _ => @"💰" // 默认使用标准Unicode
        };
    }

    /// <summary>
    /// 获取水滴表情符号 - 用于流动性或清新感表达
    /// </summary>
    /// <returns>适配当前聊天平台的水滴表情符号</returns>
    public static string GetWater()
    {
        return RuntimeConfiguration.SelectedChatApp switch
        {
            EnumChatApp.MyQQ => @"[emoji=F09F92A6]", // MyQQ平台使用自定义格式
            EnumChatApp.一起聊吧 => @"💦", // 一起聊吧平台支持直接Unicode
            _ => @"💦" // 默认使用标准Unicode
        };
    }

    /// <summary>
    /// 获取红叉表情符号 - 用于错误或失败提示
    /// </summary>
    /// <returns>适配当前聊天平台的红叉表情符号</returns>
    public static string GetCrossMark()
    {
        return RuntimeConfiguration.SelectedChatApp switch
        {
            EnumChatApp.MyQQ => @"[emoji=E29D8C]", // MyQQ平台使用自定义格式
            EnumChatApp.一起聊吧 => @"❌", // 一起聊吧平台支持直接Unicode
            _ => @"❌" // 默认使用标准Unicode
        };
    }

    /// <summary>
    /// 获取绿色对勾表情符号 - 用于成功或确认提示
    /// </summary>
    /// <returns>适配当前聊天平台的绿色对勾表情符号</returns>
    public static string GetCheckMark()
    {
        return RuntimeConfiguration.SelectedChatApp switch
        {
            EnumChatApp.MyQQ => @"[emoji=E29C85]", // MyQQ平台使用自定义格式
            EnumChatApp.一起聊吧 => @"✅", // 一起聊吧平台使用Unicode
            _ => @"✅" // 默认使用标准Unicode
        };
    }

    /// <summary>
    /// 获取太阳表情符号 - 用于阳光或积极情绪表达
    /// </summary>
    /// <returns>适配当前聊天平台的星星表情符号</returns>
    public static string GetSun()
    {
        return RuntimeConfiguration.SelectedChatApp switch
        {
            EnumChatApp.MyQQ => @"[emoji=E29880]", // MyQQ平台使用自定义格式
            EnumChatApp.一起聊吧 => @"☀️", // 一起聊吧平台使用Unicode
            _ => @"☀️" // 默认使用标准Unicode
        };
    }

    /// <summary>
    /// 获取星星表情符号 - 用于重要信息或装饰
    /// </summary>
    /// <returns>适配当前聊天平台的星星表情符号</returns>
    public static string GetStar()
    {
        return RuntimeConfiguration.SelectedChatApp switch
        {
            EnumChatApp.MyQQ => @"[emoji=E2AD90]", // MyQQ平台使用自定义格式
            EnumChatApp.一起聊吧 => @"⭐", // 一起聊吧平台使用Unicode
            _ => @"⭐" // 默认使用标准Unicode
        };
    }

    /// <summary>
    /// 获取爱心表情符号 - 用于表达喜爱或温暖情感
    /// </summary>
    /// <returns>适配当前聊天平台的爱心表情符号</returns>
    public static string GetHeart()
    {
        return RuntimeConfiguration.SelectedChatApp switch
        {
            EnumChatApp.MyQQ => @"[Face66.gif]", // MyQQ平台使用自定义格式
            EnumChatApp.一起聊吧 => @"❤️", // 一起聊吧平台使用Unicode
            _ => @"❤️" // 默认使用标准Unicode
        };
    }

    /*
     * 其他表情符号集合：
     *
     * 😀 面部表情：
     * 😀 😃 😄 😁 😆 😅 😂 🤣 😊 😇
     * 🙂 🙃 😉 😌 😍 🥰 😘 😗 😙 😚
     * 😋 😛 😝 😜 🤪 🤨 🧐 🤓 😎 🤩
     * 🥳 😏 😒 😞 😔 😟 😕 🙁 ☹️ 😣
     * 😖 😫 😩 🥺 😢 😭 😤 😠 😡 🤬
     * 🤯 😳 🥵 🥶 😱 😨 😰 😥 😓 🤗
     * 🤔 🤭 🤫 🤥 😶 😐 😑 😬 🙄 😯
     * 😦 😧 😮 😲 🥱 😴 🤤 😪 😵 🤐
     * 🥴 🤢 🤮 🤧 😷 🤒 🤕 🤑 🤠 😈
     * 👿 👹 👺 🤡 💩 👻 💀 ☠️ 👽 👾
     * 🤖 🎃 😺 😸 😹 😻 😼 😽 🙀 😿
     * 😾
     *
     * 👋 手势和身体部位：
     * 👋 🤚 🖐️ ✋ 🖖 👌 🤌 🤏 ✌️ 🤞
     * 🤟 🤘 🤙 👈 👉 👆 🖕 👇 ☝️ 👍
     * 👎 👊 ✊ 🤛 🤜 👏 🙌 👐 🤲 🤝
     * 🙏 ✍️ 💅 🤳 💪 🦾 🦿 🦵 🦶 👂
     * 🦻 👃 🧠 🫀 🫁 🦷 🦴 👀 👁️ 👅
     * 👄 💋 🩸
     *
     * 👶 人物和职业：
     * 👶 🧒 👦 👧 🧑 👱 👨 🧔 👩 🧓
     * 👴 👵 🙍 🙎 🙅 🙆 💁 🙋 🧏 🙇
     * 🤦 🤷 👮 🕵️ 💂 🥷 👷 🤴 👸 👳
     * 👲 🧕 🤵 👰 🤰 🤱 👼 🎅 🤶 🦸
     * 🦹 🧙 🧚 🧛 🧜 🧝 🧞 🧟 💆 💇
     * 🚶 🧍 🏃 💃 🕺 🕴️ 👯 🧖 🧗 🏇
     * ⛷️ 🏂 🏌️ 🏄 🚣 🏊 ⛹️ 🏋️ 🚴 🚵
     * 🤸 🤼 🤽 🤾 🤹 🧘 🛀 🛌
     *
     * ❤️ 心形和符号：
     * ❤️ 🧡 💛 💚 💙 💜 🖤 🤍 🤎 💔
     * ❣️ 💕 💞 💓 💗 💖 💘 💝 💟 ☮️
     * ✝️ ☪️ 🕉️ ☸️ ✡️ 🔯 🕎 ☯️ ☦️ 🛐
     * ⛎ ♈ ♉ ♊ ♋ ♌ ♍ ♎ ♏ ♐
     * ♑ ♒ ♓ 🆔 ⚛️ 🉑 ☢️ ☣️ 📴 📳
     * 🈶 🈚 🈸 🈺 🈷️ ✴️ 🆚 💮 🉐 ㊙️
     * ㊗️ 🈴 🈵 🈹 🈲 🅰️ 🅱️ 🆎 🆑 🅾️
     * 🆘 ❌ ⭕ 🛑 ⛔ 📛 🚫 💯 💢 ♨️
     * 🚷 🚯 🚳 🚱 🔞 📵 🚭 ❗ ❕ ❓
     * ❔ ‼️ ⁉️ 🔅 🔆 〽️ ⚠️ 🚸 🔱 ⚜️
     * 🔰 ♻️ ✅ 🈯 💹 ❇️ ✳️ ❎ 🌐 💠
     *
     * 🐶 动物和自然：
     * 🐶 🐱 🐭 🐹 🐰 🦊 🐻 🐼 🐨 🐯
     * 🦁 🐮 🐷 🐽 🐸 🐵 🙈 🙉 🙊 🐒
     * 🐔 🐧 🐦 🐤 🐣 🐥 🦆 🦅 🦉 🦇
     * 🐺 🐗 🐴 🦄 🐝 🐛 🦋 🐌 🐞 🐜
     * 🦟 🦗 🕷️ 🕸️ 🦂 🐢 🐍 🦎 🦖 🦕
     * 🐙 🦑 🦐 🦞 🦀 🐡 🐠 🐟 🐬 🐳
     * 🐋 🦈 🐊 🐅 🐆 🦓 🦍 🦧 🐘 🦛
     * 🦏 🐪 🐫 🦒 🦘 🐃 🐂 🐄 🐎 🐖
     * 🐏 🐑 🦙 🐐 🦌 🐕 🐩 🦮 🐕‍🦺 🐈
     * 🐓 🦃 🦚 🦜 🦢 🦩 🕊️ 🐇 🦝 🦨
     * 🦡 🦦 🦥 🐁 🐀 🐿️ 🦔
     *
     * 🌱 植物和食物：
     * 🌱 🌿 🍀 🍁 🍂 🍃 🌾 🌵 🌲 🌳
     * 🌴 🌸 🌺 🌻 🌷 🌹 🥀 🌼 🌙 ⭐
     * 🌟 ✨ ⚡ ☄️ 💫 🌞 ☀️ 🌤️ ⛅ 🌦️
     * 🌧️ ⛈️ 🌩️ 🌨️ ❄️ ☃️ ⛄ 🌬️ 💨 💧
     * 💦 ☔ ☂️ 🌊 🌈
     *
     * 🍎 🍐 🍊 🍋 🍌 🍉 🍇 🍓 🫐 🍈
     * 🍒 🍑 🥭 🍍 🥥 🥝 🍅 🍆 🥑 🥦
     * 🥬 🥒 🌶️ 🫑 🌽 🥕 🫒 🧄 🧅 🥔
     * 🍠 🥐 🥖 🍞 🥨 🥯 🧀 🥚 🍳 🧈
     * 🥞 🧇 🥓 🥩 🍗 🍖 🦴 🌭 🍔 🍟
     * 🍕 🫓 🥪 🥙 🧆 🌮 🌯 🫔 🥗 🥘
     * 🫕 🥫 🍝 🍜 🍲 🍛 🍣 🍱 🥟 🦪
     * 🍤 🍙 🍚 🍘 🍥 🥠 🥮 🍢 🍡 🍧
     * 🍨 🍦 🥧 🧁 🍰 🎂 🍮 🍭 🍬 🍫
     * 🍿 🍩 🍪 🌰 🥜 🍯 🥛 🍼 ☕ 🍵
     * 🧃 🥤 🍶 🍺 🍻 🥂 🍷 🥃 🍸 🍹
     * 🧉 🍾
     *
     * 🚗 交通工具：
     * 🚗 🚕 🚙 🚌 🚎 🏎️ 🚓 🚑 🚒 🚐
     * 🛻 🚚 🚛 🚜 🏍️ 🛵 🚲 🛴 🛹 🛼
     * 🚁 🚟 🚠 🚡 ⛴️ 🚤 🛥️ 🛳️ ⛵ 🚢
     * ✈️ 🛩️ 🛫 🛬 🪂 💺 🚀 🛸 🚉 🚊
     * 🚝 🚞 🚋 🚃 🚋 🚞 🚝 🚄 🚅 🚈
     * 🚂 🚆 🚇 🚊 🚉 🚁 🚟 🚠 🚡 🚖
     * 🚘 🚍 🚔 🚨 🚥 🚦 🛑 🚧 ⚓ ⛽
     * 🚏 🚇 🛣️ 🛤️ 🛢️ 🛞
     *
     * 🏠 建筑和地点：
     * 🏠 🏡 🏘️ 🏚️ 🏗️ 🏭 🏢 🏬 🏣 🏤
     * 🏥 🏦 🏨 🏪 🏫 🏩 💒 🏛️ ⛪ 🕌
     * 🕍 🛕 🕋 ⛩️ 🛤️ 🛣️ 🗾 🏞️ 🏟️ 🏛️
     * 🏗️ 🧱 🪨 🪵 🛖 🏘️ 🏚️ 🏠 🏡 🏢
     * 🏣 🏤 🏥 🏦 🏨 🏩 🏪 🏫 🏬 🏭
     * 🏯 🏰 🗼 🗽 ⛪ 🕌 🛕 🕍 ⛩️ 🕋
     * ⛲ ⛺ 🌁 🌃 🏙️ 🌄 🌅 🌆 🌇 🌉
     * 🎠 🎡 🎢 💈 🎪
     *
     * 🎮 活动和物品：
     * ⚽ 🏀 🏈 ⚾ 🥎 🎾 🏐 🏉 🥏 🎱
     * 🪀 🏓 🏸 🏒 🏑 🥍 🏏 🪃 🥅 ⛳
     * 🪁 🏹 🎣 🤿 🥊 🥋 🎽 🛹 🛷 ⛸️
     * 🥌 🎿 ⛷️ 🏂 🪂 🏋️ 🤸 🤼 🤽 🤾
     * 🤹 🎪 🎭 🩰 🎨 🎬 🎤 🎧 🎼 🎵
     * 🎶 🪘 🥁 🎷 🎺 🎸 🪕 🎻 🎹 🪗
     * 🪈 📯 🎲 🎯 🎳 🎮 🕹️ 🎰 🧩
     *
     * 📱 科技和办公：
     * 📱 📲 ☎️ 📞 📟 📠 🔋 🔌 💻 🖥️
     * 🖨️ ⌨️ 🖱️ 🖲️ 💽 💾 💿 📀 🧮 🎥
     * 📹 📷 📸 📼 🔍 🔎 🕯️ 💡 🔦 🏮
     * 🪔 📔 📕 📖 📗 📘 📙 📚 📓 📒
     * 📃 📜 📄 📰 🗞️ 📑 🔖 🏷️ 💰 🪙
     * 💴 💵 💶 💷 💸 💳 🧾 💎 ⚖️ 🪜
     * 🧰 🪛 🔧 🔨 ⚒️ 🛠️ ⛏️ 🪚 🔩 ⚙️
     * 🪤 🧲 🔫 🧨 💣 🔪 🗡️ ⚔️ 🛡️ 🚬
     * ⚰️ 🪦 ⚱️ 🏺 🔮 📿 🧿 💈 ⚗️ 🔭
     * 🔬 🕳️ 🩹 🩺 💊 💉 🧬 🦠 🧫 🧪
     * 🌡️ 🧹 🪣 🧽 🧴 🛎️ 🔑 🗝️ 🚪
     * 🪑 🛏️ 🛋️ 🪞 🚿 🛁 🚽 🪠 🧻 🪒
     * 🧼 🪥 🧴 🧷 🧹 🧺 🔱
     *
     */
}