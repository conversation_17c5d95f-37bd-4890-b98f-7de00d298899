﻿namespace CommandGuard.Configuration;

/// <summary>
/// 应用程序核心配置根类
/// 用于映射 appsettings.json 配置文件的结构
/// 包含应用程序基本信息和功能开关
/// 通过强类型配置绑定，提供类型安全的配置访问方式
/// </summary>
public class AppSettings
{
    /// <summary>
    /// 应用程序基本信息配置节
    /// 包含应用程序的基本信息和运行环境设置
    /// </summary>
    public ApplicationInfo Application { get; set; } = new();

    /// <summary>
    /// 功能开关配置节
    /// 包含各种功能模块的启用/禁用开关
    /// </summary>
    public Features Features { get; set; } = new();
}

/// <summary>
/// 应用程序基本信息配置类
/// 包含应用程序的基本信息和运行环境设置
/// </summary>
public class ApplicationInfo
{
    /// <summary>
    /// 应用程序显示名称
    /// 用于在界面标题、日志记录、关于对话框等地方显示
    /// 可以包含中文字符和特殊符号
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 应用程序版本号
    /// 遵循语义化版本控制规范（如：1.0.0）
    /// 用于版本管理、更新检查、兼容性判断等
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 运行环境标识
    /// 如：Development, Staging, Production
    /// 用于区分不同的运行环境
    /// </summary>
    public string Environment { get; set; } = "Development";

    /// <summary>
    /// 健康检查间隔时间（分钟）
    /// 默认值：5分钟
    /// 系统健康状态检查的间隔时间
    /// </summary>
    public int HealthCheckIntervalMinutes { get; set; } = 5;

    /// <summary>
    /// 是否启用调试模式
    /// true：启用详细的调试信息输出
    /// false：正常运行模式
    /// </summary>
    public bool EnableDebugMode { get; set; } = true;

    /// <summary>
    /// 应用程序文化设置
    /// 如：zh-CN, en-US, ja-JP
    /// 用于本地化和国际化
    /// </summary>
    public string Culture { get; set; } = "zh-CN";

    /// <summary>
    /// 时区设置
    /// 如：Asia/Shanghai, UTC, America/New_York
    /// 用于时间显示和计算
    /// </summary>
    public string TimeZone { get; set; } = "Asia/Shanghai";
}

/// <summary>
/// 功能开关配置类
/// 包含各种功能模块的启用/禁用开关
/// 用于控制应用程序的功能特性
/// </summary>
public class Features
{
    /// <summary>
    /// 是否启用数据库结构自动同步功能
    /// true：FreeSql会自动根据实体类创建或更新数据库表结构
    /// false：需要手动管理数据库表结构
    /// 生产环境建议设置为false，开发环境可设置为true
    /// </summary>
    public bool EnableAutoSyncStructure { get; set; } = true;

    /// <summary>
    /// 是否启用SQL语句监控功能
    /// true：在控制台输出执行的SQL语句，便于开发调试
    /// false：不输出SQL语句，适用于生产环境
    /// 生产环境必须设置为false，避免敏感信息泄露
    /// </summary>
    public bool EnableSqlMonitoring { get; set; } = false;

    /// <summary>
    /// 是否启用性能监控功能
    /// true：记录性能指标和统计信息
    /// false：不记录性能数据
    /// </summary>
    public bool EnablePerformanceMonitoring { get; set; } = true;

    /// <summary>
    /// 是否启用缓存功能
    /// true：启用内存缓存和分布式缓存
    /// false：禁用缓存功能
    /// </summary>
    public bool EnableCaching { get; set; } = true;

    /// <summary>
    /// 是否启用指标收集功能
    /// true：收集应用程序运行指标
    /// false：不收集指标数据
    /// </summary>
    public bool EnableMetrics { get; set; } = false;
}