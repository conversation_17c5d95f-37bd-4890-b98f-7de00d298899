﻿using CommandGuard.Configuration;
using CommandGuard.Constants;
using CommandGuard.Enums;
using CommandGuard.Helpers;
using CommandGuard.Interfaces.Business;
using CommandGuard.Interfaces.Chat;
using CommandGuard.Interfaces.Infrastructure;
using CommandGuard.Interfaces.Lottery;
using CommandGuard.Models;
using CommandGuard.Services.Infrastructure;
using CommandGuard.Services.Lottery;
using Microsoft.Extensions.Logging;
using System.Text;

namespace CommandGuard.Services.Business;

public class RobotService(
    ILogger<RobotService> logger,
    IMemberService memberService,
    ICommandService commandService,
    IChatService chatService,
    IMessageStorageService messageStorageService,
    IBetRecordService betRecordService,
    IDrawService drawService,
    SettlementService settlementService,
    ImageHelper imageHelper,
    ISystemSettingService systemSettingService,
    IAudioService audioService) : IRobotService
{
    /// <summary>
    /// 工作主循环
    /// </summary>
    /// <param name="cancellationToken"></param>
    public async Task DoWorkAsync(CancellationToken cancellationToken)
    {
        logger.LogInformation(@"RobotService工作循环已启动");

        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // 在每次循环开始时检查取消令牌
                    cancellationToken.ThrowIfCancellationRequested();

                    // 判断获取机器人信息是否成功
                    if (string.IsNullOrEmpty(RuntimeConfiguration.GetRobotAccount()))
                    {
                        await chatService.GetRobotInfoAsync();
                        continue;
                    }

                    // 判断机器人群组信息中是否有数据,没有则先获取机器人群组
                    if (RuntimeConfiguration.IsGroupDicEmpty())
                    {
                        logger.LogInformation(@"机器人群组信息为空，开始获取群组信息");
                        await chatService.GetGroupDicAsync();

                        // 获取群组信息后，检查是否成功获取到数据
                        var groupCount = RuntimeConfiguration.GetGroupCount();
                        if (groupCount > 0)
                        {
                            logger.LogInformation(@"成功获取机器人群组信息，共 {GroupCount} 个群组", groupCount);
                        }
                        else
                        {
                            logger.LogWarning(@"获取机器人群组信息失败或群组为空，将在下次循环重试");
                            continue;
                        }
                    }

                    // 再次检查取消令牌
                    cancellationToken.ThrowIfCancellationRequested();

                    // 获取开奖数据
                    var hasNewData = await drawService.GetDrawFromApiAsync(cancellationToken);
                    if (hasNewData)
                    {
                        logger.LogInformation(@"获取到新的开奖数据，开始处理投注结算和图片生成");

                        // 处理投注结算
                        await ProcessBetSettlementAsync();

                        // 生成开奖数据图片
                        await GenerateDrawImagesAsync(cancellationToken);

                        // 发送开奖数据和摊路图到聊天平台
                        await SendDrawResultAndRoadmapsAsync(cancellationToken);

                        // 发送开盘通知消息
                        if (RuntimeConfiguration.IsGameServiceStarted)
                        {
                            // 设置全局投注状态为true
                            RuntimeConfiguration.CanBet = true;

                            // 发送开盘通知消息
                            await chatService.SendOpenTipsAsync();

                            // 播放开奖开始答题音效
                            _ = Task.Run(async () => await audioService.PlayOpenDrawAsync());
                        }
                    }

                    // 再次检查取消令牌
                    cancellationToken.ThrowIfCancellationRequested();

                    // 处理消息队列中的待处理消息
                    var messages = await messageStorageService.GetMessagesByStatusAsync(EnumMessageStatus.Pending);
                    foreach (var message in messages)
                    {
                        try
                        {
                            // 在处理每条消息前检查取消令牌
                            cancellationToken.ThrowIfCancellationRequested();

                            // 标记消息为已处理
                            await messageStorageService.UpdateMessageStatusAsync(message.Id, EnumMessageStatus.Processed);

                            // 确保会员存在（不存在则自动创建，并自动获取昵称）
                            var member = await memberService.GetOrCreateMemberWithNickNameAsync(
                                message.Account,
                                message.NickName,
                                async account => await chatService.GetNickNameAsync(account)
                            ).ConfigureAwait(false);

                            // 处理单条消息（识别上分/下分指令）,使用新的统一指令处理方法
                            await commandService.ProcessMessageCommandAsync(message);
                        }
                        catch (OperationCanceledException)
                        {
                            logger.LogInformation(@"消息处理被取消");
                            throw;
                        }
                        catch (Exception ex)
                        {
                            // 单条消息处理失败不影响其他消息
                            logger.LogError(ex, @"处理单条消息失败，消息ID: {MessageId}", message.Id);
                        }
                    }

                    // 在循环末尾添加短暂延迟，并传递取消令牌
                    await Task.Delay(100, cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    logger.LogInformation(@"RobotService工作循环被取消");
                    break;
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, @"RobotService工作循环发生异常");
                    // 异常时也要传递取消令牌
                    await Task.Delay(1000, cancellationToken);
                }
            }
        }
        catch (OperationCanceledException)
        {
            logger.LogInformation(@"RobotService工作循环已正常取消");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"RobotService工作循环发生未处理异常");
        }
        finally
        {
            logger.LogInformation(@"RobotService工作循环已退出");
        }
    }


    #region 开奖服务相关方法

    /// <summary>
    /// 生成开奖数据图片
    /// </summary>
    private async Task GenerateDrawImagesAsync(CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation(@"开始生成开奖数据图片");

            // 获取最新开奖数据
            var latestKj = await drawService.GetDrawLastAsync(cancellationToken);

            if (string.IsNullOrEmpty(latestKj.DrawNum) || latestKj.DrawNum.Contains("--"))
            {
                logger.LogWarning(@"没有有效的开奖数据，跳过图片生成");
                return;
            }

            // 生成开奖数据图片
            await imageHelper.DrawOpenDataAsync(latestKj);
            logger.LogInformation(@"开奖数据图片生成完成");

            // 生成7行路子图
            await imageHelper.DrawTanImageAsync(latestKj, 7);
            logger.LogInformation(@"7行路子图生成完成");

            // 生成6行路子图
            await imageHelper.DrawTanImageAsync(latestKj, 6);
            logger.LogInformation(@"6行路子图生成完成");

            // 生成完整路子图（7行）
            await imageHelper.DrawTanImageFullAsync(latestKj, 7);
            logger.LogInformation(@"7行完整路子图生成完成");

            // 生成完整路子图（6行）
            await imageHelper.DrawTanImageFullAsync(latestKj, 6);
            logger.LogInformation(@"6行完整路子图生成完成");

            logger.LogInformation(@"所有开奖图片生成完成");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"生成开奖数据图片时发生错误");
        }
    }

    /// <summary>
    /// 发送开奖数据和摊路图到聊天平台
    /// </summary>
    private async Task SendDrawResultAndRoadmapsAsync(CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation(@"开始发送开奖数据和摊路图到聊天平台");

            // 检查游戏服务是否已启动
            if (!RuntimeConfiguration.IsGameServiceStarted)
            {
                logger.LogInformation(@"游戏服务未启动，跳过发送开奖图片");
                return;
            }

            // 获取系统设置，检查是否需要发送6路图和7路图
            var send6RoadMap = await systemSettingService.GetSettingValueAsync(@"发送6路图", true);
            var send7RoadMap = await systemSettingService.GetSettingValueAsync(@"发送7路图", true);

            logger.LogInformation(@"系统设置 - 发送6路图: {Send6}, 发送7路图: {Send7}", send6RoadMap, send7RoadMap);

            // 发送开奖数据图片
            if (File.Exists(ImageConstants.DrawImagePath))
            {
                await chatService.SendImageAsync(ImageConstants.DrawImagePath);
                logger.LogInformation(@"开奖数据图片发送成功: {ImagePath}", ImageConstants.DrawImagePath);

                // 图片之间添加短暂延迟，避免发送过快
                await Task.Delay(1000, cancellationToken);
            }
            else
            {
                logger.LogWarning(@"开奖数据图片不存在: {ImagePath}", ImageConstants.DrawImagePath);
            }

            // 根据设置发送6行摊路图
            if (send6RoadMap && File.Exists(ImageConstants.TanRows6ImagePath))
            {
                await chatService.SendImageAsync(ImageConstants.TanRows6ImagePath);
                logger.LogInformation(@"6行摊路图发送成功: {ImagePath}", ImageConstants.TanRows6ImagePath);

                // 图片之间添加短暂延迟
                await Task.Delay(1000, cancellationToken);
            }
            else if (send6RoadMap)
            {
                logger.LogWarning(@"6行摊路图不存在: {ImagePath}", ImageConstants.TanRows6ImagePath);
            }

            // 根据设置发送7行摊路图
            if (send7RoadMap && File.Exists(ImageConstants.TanRows7ImagePath))
            {
                await chatService.SendImageAsync(ImageConstants.TanRows7ImagePath);
                logger.LogInformation(@"7行摊路图发送成功: {ImagePath}", ImageConstants.TanRows7ImagePath);

                // 图片之间添加短暂延迟
                await Task.Delay(1000, cancellationToken);
            }
            else if (send7RoadMap)
            {
                logger.LogWarning(@"7行摊路图不存在: {ImagePath}", ImageConstants.TanRows7ImagePath);
            }

            logger.LogInformation(@"开奖数据和摊路图发送完成");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"发送开奖数据和摊路图时发生错误");
        }
    }

    #endregion

    #region 结算相关方法

    /// <summary>
    /// 根据期号获取开奖结果
    /// </summary>
    /// <param name="issue">期号</param>
    /// <returns>开奖结果，如果不存在则返回null</returns>
    private async Task<KaiJiang?> GetDrawResultByIssueAsync(string issue)
    {
        try
        {
            return await drawService.GetDrawByIssueAsync(issue, RuntimeConfiguration.RobotServiceCancellation!.Token);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"根据期号 {Issue} 获取开奖数据失败", issue);
            return null;
        }
    }

    /// <summary>
    /// 处理投注结算 - 基于未结算订单查找开奖数据
    /// </summary>
    private async Task ProcessBetSettlementAsync()
    {
        try
        {
            logger.LogInformation(@"=== 开始投注结算处理 ===");

            // 获取所有未结算的投注订单
            var unSettledOrders = await betRecordService.GetUnSettledBetOrdersAsync();

            if (!unSettledOrders.Any())
            {
                logger.LogInformation(@"没有找到未结算的投注订单");
                return;
            }

            logger.LogInformation(@"共找到 {Count} 个未结算的投注订单", unSettledOrders.Count);

            // 按期号分组
            var ordersByIssue = unSettledOrders.GroupBy(o => o.Issue).ToList();
            logger.LogInformation(@"涉及 {IssueCount} 个期号", ordersByIssue.Count);

            int totalProcessed = 0;
            int totalSettled = 0;
            int issuesProcessed = 0;

            // 遍历每个期号
            foreach (var issueGroup in ordersByIssue)
            {
                var issue = issueGroup.Key;
                var ordersForIssue = issueGroup.ToList();

                logger.LogInformation(@"检查期号 {Issue} - 有 {Count} 个未结算订单", issue, ordersForIssue.Count);

                // 检查该期号是否有开奖数据
                var drawResult = await GetDrawResultByIssueAsync(issue);
                if (drawResult == null)
                {
                    logger.LogInformation(@"期号 {Issue} 暂无开奖数据，跳过结算", issue);
                    continue;
                }

                logger.LogInformation(@"期号 {Issue} 找到开奖数据 - 开奖号码: {DrawNumbers}, 开奖时间: {Time}",
                    issue, drawResult.DrawNum, drawResult.Time);

                // 结算该期号的所有订单
                int settledCount = 0;
                foreach (var order in ordersForIssue)
                {
                    logger.LogDebug(@"开始结算订单 - ID: {OrderId}, 账号: {Account}, 投注项目: {PlayItem}, 金额: {Amount}",
                        order.Id, order.Account, order.PlayItem, order.Amount);

                    var success = await ProcessSingleBetSettlementAsync(order, drawResult);
                    if (success)
                    {
                        settledCount++;
                        totalSettled++;
                    }

                    totalProcessed++;
                }

                logger.LogInformation(@"期号 {Issue} 结算完成 - 成功: {SettledCount}/{TotalCount}",
                    issue, settledCount, ordersForIssue.Count);

                issuesProcessed++;
            }

            logger.LogInformation(@"投注结算处理完成 - 处理期号: {IssuesProcessed}, 处理订单: {TotalProcessed}, 成功结算: {TotalSettled}",
                issuesProcessed, totalProcessed, totalSettled);

            // 发送结算结果到聊天平台
            logger.LogInformation(@"检查是否需要发送结算结果 - 成功结算订单数: {TotalSettled}", totalSettled);
            if (totalSettled > 0)
            {
                logger.LogInformation(@"开始发送结算结果到聊天平台");
                await SendSettlementResultAsync();
            }
            else
            {
                logger.LogInformation(@"没有成功结算的订单，跳过发送结算结果");
            }

            logger.LogInformation(@"=== 投注结算处理结束 ===");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理投注结算时发生错误");
        }
    }

    /// <summary>
    /// 处理单个投注订单的结算
    /// </summary>
    /// <returns>结算是否成功</returns>
    private async Task<bool> ProcessSingleBetSettlementAsync(BetOrder order, KaiJiang drawResult)
    {
        try
        {
            logger.LogDebug(@"开始计算投注结果 - 订单ID: {OrderId}, 投注项目: {PlayItem}, 开奖号码: {DrawNum}",
                order.Id, order.PlayItem, drawResult.DrawNum);

            // 判断投注结果（基于台湾宾果3游戏规则）
            var (settlementResult, winAmount) = CalculateBetResult(order, drawResult);

            // 计算番摊结果
            var fanTanResult = CalculateFanTanResultFromDrawNum(drawResult.DrawNum);

            logger.LogInformation(@"投注结果计算完成 - 订单ID: {OrderId}, 结算状态: {SettlementResult}, 金额: {WinAmount}, 番摊结果: {FanTanResult}",
                order.Id, settlementResult, winAmount, fanTanResult);

            // 结算投注订单
            var settlementSuccess = await betRecordService.SettleBetOrderAsync(
                order.Id, settlementResult, winAmount, fanTanResult, @"系统自动结算");

            if (settlementSuccess)
            {
                logger.LogInformation(@"投注订单结算成功 - 订单ID: {OrderId}, 账号: {Account}, 投注项目: {PlayItem}, 结算状态: {SettlementResult}, 金额: {WinAmount}",
                    order.Id, order.Account, order.PlayItem, settlementResult, winAmount);

                return true;
            }

            logger.LogError(@"投注订单结算失败 - 订单ID: {OrderId}, 账号: {Account}, 投注项目: {PlayItem}",
                order.Id, order.Account, order.PlayItem);
            return false;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理投注订单结算异常 - 订单ID: {OrderId}, 账号: {Account}, 投注项目: {PlayItem}",
                order.Id, order.Account, order.PlayItem);
            return false;
        }
    }

    /// <summary>
    /// 计算投注结果（使用SettlementService）
    /// 根据台湾宾果3的番摊规则实现完整的中奖判断逻辑
    /// </summary>
    private (EnumBetOrderStatus settlementResult, decimal winAmount) CalculateBetResult(BetOrder order, KaiJiang drawResult)
    {
        try
        {
            // 解析开奖号码
            if (string.IsNullOrEmpty(drawResult.DrawNum) || drawResult.DrawNum.Contains("--"))
            {
                return (EnumBetOrderStatus.Lose, 0); // 无效开奖号码，视为未中奖
            }

            // 使用SettlementService计算番摊结果
            var fanTanResult = settlementService.CalculateFanTanResult(drawResult.DrawNum);

            if (fanTanResult == 0)
            {
                logger.LogWarning(@"无法计算番摊结果，订单ID: {OrderId}", order.Id);
                return (EnumBetOrderStatus.Lose, 0);
            }

            // 使用SettlementService判断投注结果
            var betResult = settlementService.JudgeBetResult(order.PlayItem, fanTanResult);

            // 转换BetResult到BetOrderStatus和计算金额
            var (settlementResult, winAmount) = ConvertBetResultToOrderStatus(betResult, order);

            logger.LogDebug(@"投注结果计算：订单ID={OrderId}, 投注项目={PlayItem}, 番摊结果={FanTanResult}, 结算状态={SettlementResult}, 金额={WinAmount}",
                order.Id, order.PlayItem, fanTanResult, settlementResult, winAmount);

            return (settlementResult, winAmount);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"计算投注结果失败，订单ID: {OrderId}", order.Id);
            return (EnumBetOrderStatus.Lose, 0);
        }
    }

    /// <summary>
    /// 将BetResult转换为BetOrderStatus和计算金额
    /// </summary>
    /// <param name="enumBetResult">投注结果</param>
    /// <param name="order">投注订单</param>
    /// <returns>结算状态和金额</returns>
    private (EnumBetOrderStatus settlementResult, decimal winAmount) ConvertBetResultToOrderStatus(EnumBetResult enumBetResult, BetOrder order)
    {
        return enumBetResult switch
        {
            EnumBetResult.Win => (EnumBetOrderStatus.Win, order.Amount * order.Odds),
            EnumBetResult.Draw => (EnumBetOrderStatus.Draw, order.Amount), // 和局退还本金
            EnumBetResult.Loss => (EnumBetOrderStatus.Lose, 0), // 未中奖无金额
            _ => (EnumBetOrderStatus.Lose, 0)
        };
    }


    /// <summary>
    /// 根据开奖号码计算番摊结果（使用SettlementService）
    /// </summary>
    /// <param name="drawNum">开奖号码字符串</param>
    /// <returns>番摊结果（1、2、3、4）</returns>
    private string CalculateFanTanResultFromDrawNum(string drawNum)
    {
        try
        {
            if (string.IsNullOrEmpty(drawNum) || drawNum.Contains("--"))
            {
                return string.Empty; // 无效开奖号码
            }

            // 使用SettlementService计算番摊结果
            var fanTanResult = settlementService.CalculateFanTanResult(drawNum);

            return fanTanResult > 0 ? fanTanResult.ToString() : string.Empty;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"计算番摊结果失败，开奖号码: {DrawNum}", drawNum);
            return string.Empty;
        }
    }

    /// <summary>
    /// 发送结算结果到聊天平台
    /// </summary>
    private async Task SendSettlementResultAsync()
    {
        try
        {
            logger.LogInformation(@"=== 开始发送结算结果到聊天平台 ===");

            // 获取最新开奖信息
            var latestDraw = await drawService.GetDrawLastAsync(CancellationToken.None);
            logger.LogInformation(@"获取最新开奖信息 - 期号: {Issue}, 开奖号码: {DrawNum}", latestDraw.Issue, latestDraw.DrawNum);

            if (latestDraw == null || string.IsNullOrEmpty(latestDraw.Issue))
            {
                logger.LogWarning(@"无法获取最新开奖信息，跳过发送结算结果");
                return;
            }

            // 查询本期有投注的会员信息
            logger.LogInformation(@"开始查询期号 {Issue} 的结算数据", latestDraw.Issue);
            var settlementData = await GetSettlementDataAsync(latestDraw.Issue);
            logger.LogInformation(@"查询到结算数据 - 参与会员数: {MemberCount}, 总积分: {TotalBalance}",
                settlementData.Members.Count, settlementData.TotalBalance);

            if (settlementData.Members.Count == 0)
            {
                logger.LogInformation(@"本期无答题记录，跳过发送结算结果");
                return;
            }

            // 格式化结算消息
            logger.LogInformation(@"开始格式化结算消息");
            var message = FormatSettlementMessage(settlementData, latestDraw);
            logger.LogInformation(@"结算消息格式化完成，消息长度: {MessageLength}", message.Length);
            logger.LogDebug(@"结算消息内容: {Message}", message);

            // 发送到聊天平台
            logger.LogInformation(@"开始发送消息到聊天平台");
            await chatService.SendGroupMessageAsync(message);

            logger.LogInformation(@"=== 结算结果发送成功 - 期号: {Issue}, 参与人数: {MemberCount} ===",
                latestDraw.Issue, settlementData.Members.Count);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"发送结算结果到聊天平台失败");
        }
    }

    /// <summary>
    /// 获取结算数据
    /// </summary>
    private async Task<SettlementData> GetSettlementDataAsync(string issue)
    {
        try
        {
            logger.LogInformation(@"开始获取期号 {Issue} 的结算数据", issue);

            // 查询本期所有投注记录（包括已结算的）
            var betOrders = await betRecordService.GetBetOrdersByIssueAsync(issue);
            logger.LogInformation(@"查询到期号 {Issue} 的投注记录数: {Count}", issue, betOrders.Count);

            if (!betOrders.Any())
            {
                logger.LogInformation(@"期号 {Issue} 没有投注记录", issue);
                return new SettlementData { Members = new List<MemberSettlementInfo>(), TotalBalance = 0 };
            }

            // 按会员分组
            var memberGroups = betOrders.GroupBy(o => o.Account).ToList();
            var memberInfos = new List<MemberSettlementInfo>();

            foreach (var memberGroup in memberGroups)
            {
                var account = memberGroup.Key;
                var orders = memberGroup.ToList();

                // 获取会员信息
                var member = await memberService.GetMemberAsync(account);
                if (member == null) continue;

                // 计算本次结算金额（正确处理和局订单）
                // 只计算已结算的订单（输、赢、和局），排除未结算和已取消的订单
                var settledOrders = orders.Where(o => o.Status == EnumBetOrderStatus.Win ||
                                                      o.Status == EnumBetOrderStatus.Lose ||
                                                      o.Status == EnumBetOrderStatus.Draw).ToList();

                var totalBetAmount = settledOrders.Sum(o => o.Amount); // 已结算订单的投注金额
                var totalWinAmount = settledOrders.Where(o => o.Status == EnumBetOrderStatus.Win).Sum(o => o.ActualWinAmount ?? 0); // 中奖金额
                var totalDrawAmount = settledOrders.Where(o => o.Status == EnumBetOrderStatus.Draw).Sum(o => o.Amount); // 和局返还金额

                // 结算金额 = 中奖金额 + 和局返还金额 - 总投注金额
                var settlementAmount = totalWinAmount + totalDrawAmount - totalBetAmount;

                // 获取中奖详情
                var winDetails = orders.Where(o => o.Status == EnumBetOrderStatus.Win && (o.ActualWinAmount ?? 0) > 0)
                    .Select(o => new WinDetail
                    {
                        PlayItem = o.PlayItem,
                        BetAmount = o.Amount,
                        WinAmount = o.ActualWinAmount ?? 0
                    }).ToList();

                memberInfos.Add(new MemberSettlementInfo
                {
                    Account = account,
                    NickName = member.NickName,
                    Balance = member.Balance,
                    SettlementAmount = settlementAmount,
                    TotalWinAmount = totalWinAmount,
                    WinDetails = winDetails
                });
            }

            // 计算总积分
            var totalBalance = memberInfos.Sum(m => m.Balance);

            return new SettlementData
            {
                Members = memberInfos,
                TotalBalance = totalBalance
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取结算数据失败 - 期号: {Issue}", issue);
            return new SettlementData { Members = new List<MemberSettlementInfo>(), TotalBalance = 0 };
        }
    }

    /// <summary>
    /// 格式化结算消息
    /// </summary>
    private string FormatSettlementMessage(SettlementData settlementData, KaiJiang drawInfo)
    {
        try
        {
            var message = new StringBuilder();

            // 第一部分：总体统计
            message.AppendLine($"在线答题人数 {settlementData.Members.Count} 总分 {settlementData.TotalBalance:F2}");

            // 第二部分：每个会员的基本信息
            foreach (var member in settlementData.Members)
            {
                message.AppendLine($"♦{member.NickName}:{member.Balance:F2} ({member.SettlementAmount:+0.00;-0.00;0.00})");
            }

            // 第三部分：分隔线
            message.AppendLine("-------------------");
            message.AppendLine("-------------------");

            // 第四部分：每个会员的中奖详情
            foreach (var member in settlementData.Members.Where(m => m.WinDetails.Any()))
            {
                string memberNickName = memberService.GetMemberAsync(member.Account).Result!.NickName;
                message.AppendLine($"[{memberNickName}]得:{member.TotalWinAmount:F2}");
                foreach (var detail in member.WinDetails)
                {
                    // 格式化投注金额：如果是整数则不显示小数点
                    var betAmountStr = detail.BetAmount % 1 == 0 ? detail.BetAmount.ToString("F0") : detail.BetAmount.ToString("F2");
                    message.AppendLine($"{detail.PlayItem}[{betAmountStr}]+{detail.WinAmount:F2}");
                }

                message.AppendLine("-------------------");
            }

            // 第五部分：开奖信息
            var (numbers, isValid) = settlementService.ParseTaiwanBingoNumbers(drawInfo.DrawNum);
            var sum = isValid ? numbers.Sum() : 0;
            var displaySum = sum > 100 ? sum % 100 : sum;
            var fanTanResult = settlementService.CalculateFanTanResult(drawInfo.DrawNum);

            // 格式化番摊结果显示
            var fanTanDisplay = fanTanResult switch
            {
                1 => "①",
                2 => "②",
                3 => "③",
                4 => "④",
                _ => "?"
            };

            message.AppendLine($"答案公布 [{drawInfo.Issue}]");
            message.AppendLine($"最好成绩【{displaySum}】【{fanTanDisplay}】摊");

            return message.ToString();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"格式化结算消息失败");
            return @"结算消息格式化失败";
        }
    }

    #endregion

    #region 结算数据类

    /// <summary>
    /// 结算数据
    /// </summary>
    private class SettlementData
    {
        public List<MemberSettlementInfo> Members { get; set; } = new();
        public decimal TotalBalance { get; set; }
    }

    /// <summary>
    /// 会员结算信息
    /// </summary>
    private class MemberSettlementInfo
    {
        public string Account { get; set; } = string.Empty;
        public string NickName { get; set; } = string.Empty;
        public decimal Balance { get; set; }
        public decimal SettlementAmount { get; set; }
        public decimal TotalWinAmount { get; set; }
        public List<WinDetail> WinDetails { get; set; } = new();
    }

    /// <summary>
    /// 中奖详情
    /// </summary>
    private class WinDetail
    {
        public string PlayItem { get; set; } = string.Empty;
        public decimal BetAmount { get; set; }
        public decimal WinAmount { get; set; }
    }

    #endregion
}