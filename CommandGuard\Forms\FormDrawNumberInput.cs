using System.Text.RegularExpressions;

namespace CommandGuard.Forms;

/// <summary>
/// 开奖号码输入对话框
/// 用于手动录入开奖号码
/// </summary>
public partial class FormDrawNumberInput : Form
{
    #region 属性

    /// <summary>
    /// 输入的开奖号码
    /// </summary>
    public string DrawNumbers { get; private set; } = string.Empty;

    /// <summary>
    /// 期号
    /// </summary>
    public string Issue { get; }

    #endregion

    #region 构造函数

    public FormDrawNumberInput(string issue)
    {
        Issue = issue;
        InitializeComponent();
        InitializeForm();
    }

    #endregion

    #region 初始化方法

    /// <summary>
    /// 初始化窗体
    /// </summary>
    private void InitializeForm()
    {
        label_Issue.Text = $@"期号：{Issue}";
        textBox_DrawNumbers.Focus();
        
        // 设置提示文本
        textBox_DrawNumbers.PlaceholderText = @"请输入20个开奖号码，用逗号分隔，如：01,02,03...";
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// 确定按钮点击事件
    /// </summary>
    private void button_OK_Click(object sender, EventArgs e)
    {
        try
        {
            var input = textBox_DrawNumbers.Text.Trim();
            
            if (string.IsNullOrEmpty(input))
            {
                MessageBox.Show(@"请输入开奖号码", @"提示", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                textBox_DrawNumbers.Focus();
                return;
            }

            // 验证开奖号码格式
            if (!ValidateDrawNumbers(input))
            {
                textBox_DrawNumbers.Focus();
                return;
            }

            DrawNumbers = input;
            DialogResult = DialogResult.OK;
            Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($@"处理开奖号码时发生错误：{ex.Message}", @"错误", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 取消按钮点击事件
    /// </summary>
    private void button_Cancel_Click(object sender, EventArgs e)
    {
        DialogResult = DialogResult.Cancel;
        Close();
    }

    /// <summary>
    /// 文本框按键事件
    /// </summary>
    private void textBox_DrawNumbers_KeyDown(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Enter)
        {
            button_OK_Click(sender, e);
        }
        else if (e.KeyCode == Keys.Escape)
        {
            button_Cancel_Click(sender, e);
        }
    }

    #endregion

    #region 验证方法

    /// <summary>
    /// 验证开奖号码格式
    /// </summary>
    /// <param name="input">输入的号码字符串</param>
    /// <returns>是否有效</returns>
    private bool ValidateDrawNumbers(string input)
    {
        try
        {
            // 检查基本格式
            if (!Regex.IsMatch(input, @"^[\d,\s]+$"))
            {
                MessageBox.Show(@"开奖号码只能包含数字和逗号", @"格式错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            // 分割号码
            var numbers = input.Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(n => n.Trim())
                .ToArray();

            // 检查数量
            if (numbers.Length != 20)
            {
                MessageBox.Show($@"必须输入20个号码，当前输入了{numbers.Length}个", @"数量错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            // 检查每个号码的有效性
            var invalidNumbers = new List<string>();
            var duplicateNumbers = new List<string>();
            var validNumbers = new HashSet<int>();

            foreach (var numberStr in numbers)
            {
                if (!int.TryParse(numberStr, out var number))
                {
                    invalidNumbers.Add(numberStr);
                    continue;
                }

                if (number < 1 || number > 80)
                {
                    invalidNumbers.Add(numberStr);
                    continue;
                }

                if (validNumbers.Contains(number))
                {
                    duplicateNumbers.Add(numberStr);
                    continue;
                }

                validNumbers.Add(number);
            }

            // 报告错误
            if (invalidNumbers.Count > 0)
            {
                MessageBox.Show($@"以下号码无效（必须是1-80之间的整数）：{string.Join(", ", invalidNumbers)}", 
                    @"号码错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (duplicateNumbers.Count > 0)
            {
                MessageBox.Show($@"以下号码重复：{string.Join(", ", duplicateNumbers)}", 
                    @"重复号码", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            MessageBox.Show($@"验证开奖号码时发生错误：{ex.Message}", @"错误", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
            return false;
        }
    }

    #endregion
}
