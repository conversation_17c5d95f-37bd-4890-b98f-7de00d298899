{"format": 1, "restore": {"F:\\RobotCopy\\SolutionCommandGuard\\CommandGuard.Licensing\\CommandGuard.Licensing.csproj": {}}, "projects": {"F:\\RobotCopy\\SolutionCommandGuard\\CommandGuard.Licensing\\CommandGuard.Licensing.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\RobotCopy\\SolutionCommandGuard\\CommandGuard.Licensing\\CommandGuard.Licensing.csproj", "projectName": "CommandGuard.<PERSON>", "projectPath": "F:\\RobotCopy\\SolutionCommandGuard\\CommandGuard.Licensing\\CommandGuard.Licensing.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\RobotCopy\\SolutionCommandGuard\\CommandGuard.Licensing\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"System.Management": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}